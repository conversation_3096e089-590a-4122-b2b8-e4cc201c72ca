import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { SessionProvider } from "@/app/components/providers/session-provider";
import { ThemeProvider } from "@/app/components/providers/theme-provider";
import ToasterProvider from "@/app/components/providers/toaster-provider";
import { auth } from "@/auth";
import { MidtransProvider } from "@/app/components/providers/midtrans-provider";

const inter = Inter({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Rental Ganset | Aplikasi Penyewaan Genset",
  description: "Solusi rental genset terpercaya untuk berbagai kebutuhan listrik Anda",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Menggunakan try-catch untuk mencegah error dari auth() mematikan aplikasi
  let session = null;
  try {
    session = await auth();
  } catch (error) {
    console.error("Error in auth() call:", error);
    // Tidak perlu throw error, biarkan aplikasi tetap berjalan
  }
  
  // Script untuk menangani tema
  const themeScript = `
    (function() {
      // Coba mengambil tema dari localStorage
      function getTheme() {
        try {
          return localStorage.getItem('rental-genset-theme') || 'light';
        } catch (e) {
          return 'light';
        }
      }
      
      // Fungsi untuk menerapkan tema
      function applyTheme(theme) {
        const d = document.documentElement;
        
        // Hapus kelas tema lama
        d.classList.remove('light', 'dark');
        
        // Tambahkan kelas untuk tema baru
        d.classList.add(theme);
        
        // Atur atribut data-theme
        d.setAttribute('data-theme', theme);
      }
      
      // Terapkan tema yang tersimpan
      var savedTheme = getTheme();
      applyTheme(savedTheme);
      
      // Tambahkan listener untuk perubahan tema
      document.addEventListener('themeChange', function(e) {
        if (e.detail && e.detail.theme) {
          applyTheme(e.detail.theme);
        }
      });
    })();
  `;

  // Script untuk menangani error fetch dari Next-Auth
  const errorHandlingScript = `
    (function() {
      // Error handling untuk fetch
      window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.toString().includes('Failed to fetch')) {
          console.warn('Unhandled fetch error detected:', event.reason);
          
          // Mencegah error mempengaruhi UI
          event.preventDefault();
          event.stopPropagation();
        }
      });
    })();
  `;
  
  return (
    <html lang="id" suppressHydrationWarning>
      <head>
        {/* Script untuk menerapkan tema sebelum hydration untuk menghindari flicker */}
        <script dangerouslySetInnerHTML={{ __html: themeScript }} />
        {/* Script untuk menangani error fetch */}
        <script dangerouslySetInnerHTML={{ __html: errorHandlingScript }} />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <SessionProvider session={session}>
          <ThemeProvider
            attribute="data-theme"
            defaultTheme="light"
            enableSystem
            storageKey="rental-genset-theme"
          >
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
              {children}
            </div>
            <ToasterProvider />
            <MidtransProvider />
          </ThemeProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
