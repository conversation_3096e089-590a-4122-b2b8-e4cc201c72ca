"use client";

import Link from "next/link";
import { But<PERSON> } from "@/app/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/app/components/ui/dropdown-menu";
import { MoreVertical, FileText, Download } from "lucide-react";

interface PaymentActionsMenuProps {
  paymentId: string;
  isPending: boolean;
  needsRemainingPayment: boolean;
}

export function PaymentActionsMenu({
  paymentId,
  isPending,
  needsRemainingPayment
}: PaymentActionsMenuProps) {

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 hover:bg-accent hover:text-accent-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          <MoreVertical className="h-4 w-4" />
          <span className="sr-only">Buka menu aksi</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-48 bg-popover border-border shadow-lg"
      >
        <DropdownMenuItem asChild>
          <Link
            href={`/admin/payments/${paymentId}`}
            className="flex items-center space-x-2 text-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer"
          >
            <FileText className="h-4 w-4" />
            <span>Lihat Detail</span>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator className="bg-border" />

        {isPending && (
          <DropdownMenuItem asChild>
            <Link
              href={`/admin/payments/${paymentId}/create-invoice`}
              className="flex items-center space-x-2 text-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer"
            >
              <Download className="h-4 w-4" />
              <span>Buat Invoice</span>
            </Link>
          </DropdownMenuItem>
        )}

        {needsRemainingPayment && (
          <DropdownMenuItem asChild>
            <Link
              href={`/admin/payments/${paymentId}/remaining-invoice`}
              className="flex items-center space-x-2 text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-900/20 cursor-pointer"
            >
              <Download className="h-4 w-4" />
              <span>Buat Invoice</span>
            </Link>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
