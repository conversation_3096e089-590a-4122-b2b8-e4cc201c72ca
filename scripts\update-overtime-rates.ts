/**
 * <PERSON>ript to update existing products with null overtime rates to 0
 * This ensures all products have a default overtime rate value
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateOvertimeRates() {
  try {
    console.log('🔄 Starting overtime rate update...');

    // Find all products with null or zero overtime rates
    const productsWithNullRates = await prisma.product.findMany({
      where: {
        overtimeRate: null
      },
      select: {
        id: true,
        name: true,
        overtimeRate: true
      }
    });

    const productsWithZeroRates = await prisma.product.findMany({
      where: {
        overtimeRate: 0
      },
      select: {
        id: true,
        name: true,
        overtimeRate: true
      }
    });

    console.log(`📊 Found ${productsWithNullRates.length} products with null overtime rates`);
    console.log(`📊 Found ${productsWithZeroRates.length} products with zero overtime rates`);

    if (productsWithNullRates.length === 0 && productsWithZeroRates.length === 0) {
      console.log('✅ All products already have proper overtime rates set');
      return;
    }

    // Update all products with null overtime rates to 50000 (50k per hour)
    const updateResult = await prisma.product.updateMany({
      where: {
        overtimeRate: null
      },
      data: {
        overtimeRate: 50000
      }
    });

    // Also update products with zero overtime rates to 50000
    const updateZeroResult = await prisma.product.updateMany({
      where: {
        overtimeRate: 0
      },
      data: {
        overtimeRate: 50000
      }
    });

    console.log(`✅ Successfully updated ${updateResult.count} products with null overtime rates`);
    console.log(`✅ Successfully updated ${updateZeroResult.count} products with zero overtime rates`);

    // Verify the update
    const remainingNullRates = await prisma.product.count({
      where: {
        overtimeRate: null
      }
    });

    if (remainingNullRates === 0) {
      console.log('🎉 All products now have overtime rates set!');
    } else {
      console.log(`⚠️  Warning: ${remainingNullRates} products still have null overtime rates`);
    }

  } catch (error) {
    console.error('❌ Error updating overtime rates:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  updateOvertimeRates()
    .then(() => {
      console.log('🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { updateOvertimeRates };
