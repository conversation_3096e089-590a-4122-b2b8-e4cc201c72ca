/* Dashboard styling */
:root,
[data-theme="light"] {
    /* Light Theme */
    --card-bg: #ffffff;
    --card-hover-shadow: rgba(0, 0, 0, 0.1);
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --green-bg: rgba(16, 185, 129, 0.1);
    --green-text: rgb(16, 185, 129);
    --green-dark: rgb(4, 120, 87);
    --orange-bg: rgba(245, 158, 11, 0.1);
    --orange-text: rgb(245, 158, 11);
    --blue-bg: rgba(59, 130, 246, 0.1);
    --blue-text: rgb(59, 130, 246);
    --grid-stroke: #e5e7eb;
    --axis-stroke: #888888;
    --tooltip-bg: #ffffff;
    --tooltip-border: #e5e7eb;
    --chart-cursor: #f3f4f6;
    --navbar-bg: #ffffff;
    --sidebar-bg: #ffffff;
}

[data-theme="dark"] {
    /* Dark Theme */
    --card-bg: #1f2937;
    --card-hover-shadow: rgba(0, 0, 0, 0.25);
    --text-primary: #f9fafb;
    --text-secondary: #9ca3af;
    --green-bg: rgba(16, 185, 129, 0.15);
    --green-text: rgb(34, 197, 94);
    --green-dark: rgb(74, 222, 128);
    --orange-bg: rgba(245, 158, 11, 0.15);
    --orange-text: rgb(249, 115, 22);
    --blue-bg: rgba(59, 130, 246, 0.15);
    --blue-text: rgb(96, 165, 250);
    --grid-stroke: #374151;
    --axis-stroke: #9ca3af;
    --tooltip-bg: #1f2937;
    --tooltip-border: #374151;
    --chart-cursor: #374151;
    --navbar-bg: #1f2937;
    --sidebar-bg: #1f2937;
}

/* Smooth theme transition */
* {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.2s ease;
}

/* Fix untuk layout sidebar */
aside {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    z-index: 10;
}

aside div.flex-col {
    background-color: var(--sidebar-bg);
}

/* Fix untuk header dan navbar */
main>div:first-child {
    background-color: var(--navbar-bg);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    z-index: 5;
}

/* Dashboard CSS untuk body */
html body {
    background-color: var(--background, #f9fafb);
}

.dark html body {
    background-color: var(--background, #111827);
}

/* Fix untuk tampilan typography */
h1,
h2,
h3 {
    color: var(--text-primary);
    margin: 0;
}

h1.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
    letter-spacing: -0.025em;
}

/* Fix untuk cards */
.card-hover {
    background-color: var(--card-bg);
    transition: all 0.3s ease;
    border: 1px solid #f3f4f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    overflow: hidden;
}

[data-theme="dark"] .card-hover {
    border-color: rgba(31, 41, 55, 0.5);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.card-hover:hover {
    box-shadow: 0 4px 12px var(--card-hover-shadow);
    transform: translateY(-2px);
}

/* Icon box styling */
.icon-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 9999px;
}

.icon-box-green {
    background-color: var(--green-bg);
    color: var(--green-text);
}

.icon-box-orange {
    background-color: var(--orange-bg);
    color: var(--orange-text);
}

.icon-box-blue {
    background-color: var(--blue-bg);
    color: var(--blue-text);
}

/* Stats styling */
.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.stat-value-green {
    color: var(--green-text);
}

.stat-value-orange {
    color: var(--orange-text);
}

.stat-value-blue {
    color: var(--blue-text);
}

.stat-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1rem;
}

.chart-container {
    margin-top: 2rem;
}

.card-link {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--green-text);
    transition: color 0.2s ease;
}

.card-link:hover {
    color: var(--green-dark);
    text-decoration: underline;
}

.card-link svg {
    margin-left: 0.25rem;
    height: 1rem;
    width: 1rem;
}