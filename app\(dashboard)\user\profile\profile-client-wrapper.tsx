"use client";

import { useEffect, useState } from "react";

// Interface untuk data session user dari server
interface SessionUser {
  id: string;
  name: string;
  email: string;
  phone: string;
  image: string;
  role: string;
  address: string;
  bio: string;
  joinDate: string;
}

// Interface untuk data profil lengkap (termasuk data dari localStorage)
interface ProfileData {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  bio: string;
  avatarUrl: string;
  role: string;
  joinDate: string;
}

// Props untuk komponen ProfileClientWrapper
interface ProfileClientWrapperProps {
  sessionUser: SessionUser;
  children: (data: ProfileData) => React.ReactNode;
}

export function ProfileClientWrapper({ sessionUser, children }: ProfileClientWrapperProps) {
  // State untuk menyimpan data profil lengkap
  const [profileData, setProfileData] = useState<ProfileData>({
    id: sessionUser.id,
    name: sessionUser.name,
    email: sessionUser.email,
    phone: sessionUser.phone,
    address: sessionUser.address,
    bio: sessionUser.bio,
    avatarUrl: sessionUser.image,
    role: sessionUser.role,
    joinDate: sessionUser.joinDate
  });

  // Effect untuk memuat data dari localStorage saat komponen dimount
  useEffect(() => {
    // Fungsi untuk memuat data profil dari localStorage
    const loadProfileData = () => {
      try {
        const storedData = localStorage.getItem('profileData');
        
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          
          // Update state dengan data dari localStorage jika ada
          setProfileData(prev => ({
            ...prev,
            address: parsedData.address || sessionUser.address,
            bio: parsedData.bio || sessionUser.bio
          }));
          
          console.log('Loaded profile data from localStorage:', parsedData);
        }
      } catch (error) {
        console.error('Error loading profile data from localStorage:', error);
      }
    };

    // Listener untuk storage events
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'profileData') {
        loadProfileData();
      }
    };
    
    // Load data on mount
    loadProfileData();
    
    // Add storage event listener
    window.addEventListener('storage', handleStorageChange);
    
    // Clean up
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [sessionUser.address, sessionUser.bio]);

  return <>{children(profileData)}</>;
} 
