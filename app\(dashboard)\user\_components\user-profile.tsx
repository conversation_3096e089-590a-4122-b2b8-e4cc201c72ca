'use client';

import { useSession } from 'next-auth/react';
import { signOut } from 'next-auth/react';
import { LuLogOut } from 'react-icons/lu';

export function UserProfile() {
    const { data: session } = useSession();
    const name = session?.user?.name || '';
    const initial = name.charAt(0).toUpperCase();

    return (
        <div className="hidden md:flex items-center gap-4">
            <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
                    {initial}
                </div>
                <span className="ml-2 text-sm font-medium">{name}</span>
            </div>
            <button
                onClick={() => signOut()}
                className="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700"
            >
                <LuLogOut className="w-4 h-4 mr-2" />
                Keluar
            </button>
        </div>
    );
} 
