"use client";

import { useCallback } from "react";
import { AddressPicker } from "@/app/components/map/AddressPicker";
import { LocationData } from "@/lib/types/map";

// Interface untuk props komponen MapLibreUi
interface MapLibreUiProps {
  onSelectLocation: (location: LocationData) => void;
  defaultAddress?: string;
}

export function MapLibreUi({ onSelectLocation, defaultAddress = "" }: MapLibreUiProps) {
  const handleLocationSelect = useCallback((location: LocationData) => {
    onSelectLocation(location);
  }, [onSelectLocation]);
  
  return (
    <div className="w-full">
      <AddressPicker 
        onSelectLocation={handleLocationSelect} 
        defaultAddress={defaultAddress} 
      />
    </div>
  );
}

export default MapLibreUi; 
