import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkOvertimeRates() {
  try {
    console.log('🔍 Checking overtime rates in database...\n');
    
    // Get all products with their overtime rates
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        price: true,
        overtimeRate: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 Found ${products.length} products:\n`);

    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   ID: ${product.id}`);
      console.log(`   Price: Rp ${product.price.toLocaleString('id-ID')}`);
      console.log(`   Overtime Rate: ${product.overtimeRate === null ? 'NULL' : `Rp ${product.overtimeRate.toLocaleString('id-ID')}`}`);
      console.log(`   Created: ${product.createdAt.toISOString()}`);
      console.log(`   Updated: ${product.updatedAt.toISOString()}`);
      console.log('');
    });

    // Check for null overtime rates
    const nullOvertimeProducts = products.filter(p => p.overtimeRate === null);
    const zeroOvertimeProducts = products.filter(p => p.overtimeRate === 0);
    const positiveOvertimeProducts = products.filter(p => p.overtimeRate && p.overtimeRate > 0);

    console.log('📈 Summary:');
    console.log(`   Products with NULL overtime rate: ${nullOvertimeProducts.length}`);
    console.log(`   Products with ZERO overtime rate: ${zeroOvertimeProducts.length}`);
    console.log(`   Products with POSITIVE overtime rate: ${positiveOvertimeProducts.length}`);

  } catch (error) {
    console.error('❌ Error checking overtime rates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOvertimeRates();
