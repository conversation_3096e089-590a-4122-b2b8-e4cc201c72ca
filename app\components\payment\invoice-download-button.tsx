"use client";

import { Button } from "@/app/components/ui/button";
import { LuDownload } from "react-icons/lu";
import { useToast } from "@/lib/hooks/use-toast";

interface InvoiceDownloadButtonProps {
  invoiceId: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  label?: string;
  fullWidth?: boolean;
}

export function InvoiceDownloadButton({
  invoiceId,
  variant = "outline",
  label = "Download Invoice",
  fullWidth = false
}: InvoiceDownloadButtonProps) {
  const { showError } = useToast();

  const handleDownload = () => {
    try {
      // Buka invoice PDF di tab baru untuk download
      window.open(`/api/invoices/${invoiceId}/pdf`, '_blank');
    } catch (error) {
      console.error("Error downloading invoice:", error);
      showError("Gagal Download Invoice. Terjadi kes<PERSON>han saat mengunduh invoice. Silakan coba lagi nanti.");
    }
  };

  return (
    <Button
      variant={variant}
      onClick={handleDownload}
      className={`${
        variant === "outline" ? "border-gray-200 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700" : ""
      } ${fullWidth ? "w-full" : ""}`}
    >
      <LuDownload className="mr-2 h-4 w-4" />
      {label}
    </Button>
  );
}
