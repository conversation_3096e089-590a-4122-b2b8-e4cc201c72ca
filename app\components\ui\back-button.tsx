"use client";

import { useRouter } from 'next/navigation';
import { Button } from '@/app/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface BackButtonProps {
  href?: string;
  label?: string;
}

export function BackButton({ 
  href, 
  label = "Kembali" 
}: BackButtonProps) {
  const router = useRouter();

  const handleClick = () => {
    if (href) {
      router.push(href);
    } else {
      router.back();
    }
  };

  return (
    <Button
      variant="ghost"
      onClick={handleClick}
      className="mb-4 gap-2 hover:bg-violet-50 dark:hover:bg-violet-900/40 text-gray-800 dark:text-violet-300"
    >
      <ArrowLeft size={16} className="text-gray-600 dark:text-violet-400" />
      {label}
    </Button>
  );
}
