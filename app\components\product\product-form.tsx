"use client";
import { useState, useEffect } from "react";
import { updateProduct, createProduct } from "@/lib/actions/product";
import { Product } from "@/lib/types/product";
import UploadForm from '../shared/upload-form';
import { toast } from "react-hot-toast";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { ProductFormFields } from "./product-form-fields";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Alert } from "@/app/components/ui/alert";
import { LoadingSpinner } from "@/app/components/ui/loading-spinner";
import {
  LuSave,
  LuRefreshCw,
  LuImage,
  LuX,
  LuCheck,
  LuArrowLeft
} from "react-icons/lu";

interface ProductFormProps {
  productId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ProductForm({ productId, onSuccess, onCancel }: ProductFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [product, setProduct] = useState<Product | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isFormValid, setIsFormValid] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const { data: session } = useSession();
  const router = useRouter();

  const fetchProduct = async (id: string) => {
    const res = await fetch(`/api/products/${id}`);
    if (!res.ok) throw new Error('Failed to fetch product');
    return res.json();
  };

  useEffect(() => {
    if (productId) {
      fetchProduct(productId).then(data => {
        if (data) {
          setProduct(data);
          if (data.imageUrl) {
            setImageUrl(data.imageUrl);
          }
        }
      });
    }
  }, [productId]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Reset previous states
    setSaveSuccess(false);
    setFormErrors({});

    // Periksa apakah user sudah login
    if (!session?.user) {
      toast.error('Silakan login terlebih dahulu');
      router.push('/login');
      return;
    }

    // Validate form before submission
    if (!isFormValid) {
      toast.error('Mohon lengkapi semua field yang wajib diisi');
      return;
    }

    setIsLoading(true);

    try {
      const formData = new FormData(event.currentTarget);
      if (imageUrl) {
        formData.append('imageUrl', imageUrl);
      }

      // Debugging
      const formDataObj = Object.fromEntries(formData);
      console.log('FormData yang akan dikirim:', formDataObj);

      // Pastikan data-data wajib sudah diisi
      if (!formDataObj.name || !formDataObj.capacity || !formDataObj.price || !formDataObj.stock) {
        throw new Error('Data produk tidak lengkap');
      }

      if (productId) {
        await updateProduct(productId, formData);
      } else {
        console.log('Creating new product with form data:', Object.fromEntries(formData));

        try {
          // First try with API endpoint - kirim sebagai FormData
          const resp = await fetch('/api/products', {
            method: 'POST',
            body: formData,
            credentials: 'include' // Penting untuk mengirim cookie session
            // Biarkan browser menentukan Content-Type secara otomatis untuk FormData
          });

          const responseText = await resp.text();
          console.log('API Response raw text:', responseText);

          if (!resp.ok) {
            let errorMessage = 'Gagal menambahkan produk melalui API';
            try {
              if (responseText) {
                const errorData = JSON.parse(responseText);
                errorMessage = errorData.message || errorData.error || errorMessage;
              }
            } catch (e) {
              console.error('Error parsing response:', e);
            }
            console.warn('API endpoint failed, trying server action instead:', errorMessage);

            // Fall back to server action if API endpoint fails
            console.log('Fallback to server action, session data:', session);

            const cleanFormData = new FormData();

            // Data wajib
            cleanFormData.append('name', String(formData.get('name')));
            cleanFormData.append('capacity', String(formData.get('capacity')));
            cleanFormData.append('price', String(formData.get('price')));
            cleanFormData.append('stock', String(formData.get('stock') || 0));

            // Data opsional
            if (formData.get('description')) {
              cleanFormData.append('description', String(formData.get('description')));
            }

            if (imageUrl) {
              cleanFormData.append('imageUrl', imageUrl);
            }

            console.log('Clean form data untuk server action:', Object.fromEntries(cleanFormData));

            const result = await createProduct({}, cleanFormData);
            console.log('Server action result:', result);

            if (!result.success) {
              // Jika user tidak ditemukan, coba refresh session
              if (result.error === "User tidak ditemukan") {
                toast.error('Sesi login tidak valid, silakan login ulang');
                router.push('/login');
                return;
              }
              throw new Error(result.error || 'Gagal menambahkan produk');
            }
          }
        } catch (error) {
          console.error('All product creation methods failed:', error);
          throw error;
        }
      }

      // Set success state
      setSaveSuccess(true);
      toast.success(productId ? 'Produk berhasil diupdate!' : 'Produk berhasil ditambahkan!');

      // Reset success state after 3 seconds
      setTimeout(() => setSaveSuccess(false), 3000);

      // Pastikan callback onSuccess dijalankan
      if (onSuccess) {
        try {
          console.log('Menjalankan callback onSuccess');
          onSuccess();
        } catch (error) {
          console.error('Error saat memanggil onSuccess:', error);
          // Jika callback error, lakukan refresh manual
          window.location.reload();
        }
      } else {
        // Jika tidak ada callback, refresh manual
        console.log('Tidak ada callback onSuccess, merefresh halaman');
        window.location.reload();
      }

    } catch (error) {
      console.error('Error saving product:', error);
      const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan saat menyimpan produk';

      // Set form errors if available
      if (error instanceof Error && error.message.includes('validation')) {
        setFormErrors({ general: errorMessage });
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form validation changes
  const handleValidationChange = (isValid: boolean) => {
    setIsFormValid(isValid);
  };

  // Handle cancel action
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Form Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {productId ? 'Edit Produk' : 'Tambah Produk Baru'}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {productId
              ? 'Perbarui informasi produk genset'
              : 'Tambahkan produk genset baru ke katalog'
            }
          </p>
        </div>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <LuArrowLeft className="h-4 w-4" />
            Kembali
          </Button>
        )}
      </div>

      {/* General Error Alert */}
      {formErrors.general && (
        <Alert className="border-red-200 bg-red-50 dark:bg-red-950/50">
          <LuX className="h-4 w-4 text-red-600" />
          <p className="text-sm text-red-800 dark:text-red-200">
            {formErrors.general}
          </p>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Image Upload Section */}
        <Card className="border bg-white dark:bg-gray-950 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
              <LuImage className="h-5 w-5 text-blue-500" />
              Gambar Produk
            </CardTitle>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Upload gambar produk yang menarik untuk menarik perhatian pelanggan
            </p>
          </CardHeader>
          <CardContent>
            <UploadForm
              onImageUploaded={(url) => setImageUrl(url)}
              currentImageUrl={imageUrl}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Format yang didukung: JPG, PNG, WebP. Maksimal 5MB. Rasio 16:9 direkomendasikan.
            </p>
          </CardContent>
        </Card>

        {/* Product Information Section */}
        <ProductFormFields
          product={product}
          errors={formErrors}
          onValidationChange={handleValidationChange}
        />

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="flex items-center justify-center gap-2 h-12 text-base"
            disabled={isLoading}
          >
            <LuArrowLeft className="h-4 w-4" />
            Batalkan
          </Button>

          <Button
            type="submit"
            disabled={isLoading || !isFormValid}
            className={`flex items-center justify-center gap-2 h-12 text-base flex-1 ${
              saveSuccess
                ? 'bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800'
                : 'bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800'
            } transition-colors duration-200`}
          >
            {isLoading ? (
              <>
                <LuRefreshCw className="h-4 w-4 animate-spin" />
                Menyimpan...
              </>
            ) : saveSuccess ? (
              <>
                <LuCheck className="h-4 w-4" />
                Tersimpan!
              </>
            ) : (
              <>
                <LuSave className="h-4 w-4" />
                {productId ? 'Update Produk' : 'Tambah Produk'}
              </>
            )}
          </Button>
        </div>
      </form>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl">
            <div className="flex items-center gap-3">
              <LoadingSpinner size="sm" />
              <p className="text-sm font-medium">
                {productId ? 'Mengupdate produk...' : 'Menambahkan produk...'}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
