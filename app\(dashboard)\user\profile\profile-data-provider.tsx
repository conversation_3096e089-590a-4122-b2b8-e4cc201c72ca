"use client";

import { useEffect, useState } from "react";

// Tipe data untuk prop ProfileDisplay
type ProfileDataProps = {
  serverAddress: string;
  serverBio: string;
  sessionUserName: string;
  sessionUserEmail: string;
  sessionUserPhone: string;
  sessionUserImage: string;
  sessionUserRole: string;
  sessionUserJoinDate: string;
};

// Tipe data profil lengkap (termasuk data dari localStorage)
type FullProfileData = {
  name: string;
  email: string;
  phone: string;
  address: string;
  bio: string;
  avatarUrl: string;
  role: string;
  joinDate: string;
};

export function ProfileDataProvider({ 
  serverAddress,
  serverBio,
  sessionUserName,
  sessionUserEmail,
  sessionUserPhone,
  sessionUserImage,
  sessionUserRole,
  sessionUserJoinDate,
  children 
}: ProfileDataProps & { children: (data: FullProfileData) => React.ReactNode }) {
  // State untuk menyimpan data profil lengkap
  const [profileData, setProfileData] = useState<FullProfileData>({
    name: sessionUser<PERSON><PERSON>,
    email: sessionUserEmail,
    phone: sessionUser<PERSON><PERSON>,
    address: server<PERSON>ddress,
    bio: serverBio,
    avatarUrl: sessionUserImage,
    role: sessionUserRole,
    joinDate: sessionUserJoinDate
  });

  // Efek untuk memuat data dari localStorage saat komponen dimuat
  useEffect(() => {
    // Fungsi untuk memuat data profil dari localStorage
    const loadProfileData = () => {
      try {
        // Coba ambil data dari localStorage
        const storedData = localStorage.getItem('profileData');
        
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          
          // Update state dengan data dari localStorage
          setProfileData(prev => ({
            ...prev,
            address: parsedData.address || serverAddress,
            bio: parsedData.bio || serverBio
          }));
        }
      } catch (error) {
        console.error('Error loading profile data:', error);
      }
    };
    
    // Setel listener untuk storage events (jika localStorage diubah oleh tab lain)
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'profileData') {
        loadProfileData();
      }
    };
    
    // Muat data saat komponen dimount
    loadProfileData();
    
    // Tambahkan listener
    window.addEventListener('storage', handleStorageChange);
    
    // Bersihkan listener saat komponen unmount
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [serverAddress, serverBio]);

  return <>{children(profileData)}</>;
} 
