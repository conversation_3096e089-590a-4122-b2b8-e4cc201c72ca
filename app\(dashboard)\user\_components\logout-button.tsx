"use client";

import { signOut } from "next-auth/react";
import { Button } from "@/app/components/ui/button";
import { LuLogOut } from "react-icons/lu";
import { cn } from "@/lib/utils/cn";

interface LogoutButtonProps {
  className?: string;
}

export function LogoutButton({ className }: LogoutButtonProps) {
  return (
    <Button 
      onClick={() => signOut({ callbackUrl: "/" })}
      variant="destructive" 
      className={cn("flex items-center gap-2", className)}>
      <LuLogOut className="h-4 w-4" />
      Keluar
    </Button>
  );
}
