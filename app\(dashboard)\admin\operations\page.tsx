import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Card, CardContent } from "@/app/components/ui/card";
import { IoPower, IoTime, IoStatsChart } from "react-icons/io5";
import { getAllOperations } from "@/lib/data/operation";
import { getOperationStatusCounts } from "@/lib/utils/operation-status";
import { OperationStatusBadge } from "@/app/components/operation/operation-status-badge";
import { AutoRefresh, ManualRefreshButton } from "@/app/components/operation/auto-refresh";
import Link from "next/link";
import { formatCurrency, formatDate } from "@/lib/utils/format";

// Pastikan halaman selalu diperbarui
export const dynamic = 'force-dynamic';

export default async function AdminOperationsPage() {
  const session = await auth();
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Gunakan fungsi getAllOperations dari lib/data/operation.ts (already sorted by priority)
  const operations = await getAllOperations();

  // Get status counts using the unified status logic
  const statusCounts = getOperationStatusCounts(operations);

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Manajemen Operasi</h1>
          <AutoRefresh intervalMs={30000} enabled={true} showIndicator={true} />
        </div>
        <div className="flex items-center gap-3">
          <ManualRefreshButton />
        </div>
      </div>

      {/* Status Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-600 text-sm font-medium dark:text-yellow-400">Menunggu Operasi</p>
                <p className="text-3xl font-bold text-yellow-700 dark:text-yellow-300">{statusCounts.pending}</p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-full flex items-center justify-center dark:bg-yellow-800">
                <IoPower className="h-6 w-6 text-yellow-500 dark:text-yellow-300" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium dark:text-blue-400">Sedang Beroperasi</p>
                <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">{statusCounts.running}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center dark:bg-blue-800">
                <IoStatsChart className="h-6 w-6 text-blue-500 dark:text-blue-300" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium dark:text-purple-400">Operasi Selesai</p>
                <p className="text-3xl font-bold text-purple-700 dark:text-purple-300">{statusCounts.completed}</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center dark:bg-purple-800">
                <IoTime className="h-6 w-6 text-purple-500 dark:text-purple-300" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <h2 className="text-xl font-medium mb-4">Daftar Operasi</h2>
      <div className="grid gap-6">
        {operations.map((rental) => {

          return (
            <Link key={rental.id} href={`/admin/operations/${rental.id}`}>
              <Card className="hover:shadow-lg transition-all duration-300 border dark:border-gray-800">
                <CardContent className="p-6">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <h3 className="text-lg font-semibold dark:text-white">{rental.product.name}</h3>
                      <p className="text-gray-600 dark:text-gray-400">{rental.product.capacity} KVA</p>
                    </div>
                    <OperationStatusBadge rental={rental} />
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    {/* Informasi customer */}
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded">
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Informasi Customer</p>
                      <p className="text-sm dark:text-gray-200">{rental.user.name}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{rental.user.email}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{rental.user.phone}</p>
                    </div>

                    {/* Informasi waktu operasi */}
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded">
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Informasi Waktu Operasi</p>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Periode Rental:</span>
                          <span className="dark:text-gray-200">{formatDate(rental.startDate)} - {formatDate(rental.endDate)}</span>
                        </div>

                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Waktu Mulai:</span>
                          <span className="font-medium dark:text-gray-200">
                            {rental.operationalStart
                              ? new Date(rental.operationalStart).toLocaleString('id-ID')
                              : 'Belum dimulai'}
                          </span>
                        </div>

                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Waktu Selesai:</span>
                          <span className="dark:text-gray-200">
                            {rental.operationalEnd
                              ? new Date(rental.operationalEnd).toLocaleString('id-ID')
                              : rental.operationalStart ? 'Sedang berlangsung' : '-'}
                          </span>
                        </div>

                        {rental.overtime && rental.overtime > 0 && (
                          <div className="flex justify-between text-sm text-orange-600 dark:text-orange-400">
                            <span>Overtime:</span>
                            <span>{rental.overtime} jam</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Jumlah Pembayaran */}
                  <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <span className="font-medium dark:text-gray-300">Total:</span>
                    <span className="font-bold text-lg text-blue-700 dark:text-blue-400">
                      {formatCurrency(rental.amount + (rental.payment?.overtime || 0))}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </Link>
          );
        })}

        {operations.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <p className="text-gray-500">Belum ada operasi rental yang aktif</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
