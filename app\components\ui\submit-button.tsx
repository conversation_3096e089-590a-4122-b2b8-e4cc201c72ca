"use client";

import React from "react";
import { useFormStatus } from "react-dom";
import { Button } from "@/app/components/ui/button";

export function SubmitButton({ 
  children, 
  className, 
  pendingText = "Menyimpan...", 
  ...props 
}: React.ButtonHTMLAttributes<HTMLButtonElement> & { 
  pendingText?: string 
}) {
  const { pending } = useFormStatus();
  
  return (
    <Button 
      type="submit" 
      disabled={pending} 
      className={className} 
      {...props}
    >
      {pending ? pendingText : children}
    </Button>
  );
} 
