"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils/cn";
import { Button } from "@/app/components/ui/button";
import { LuMenu, LuLayoutDashboard as LuDashboard, LuShoppingCart, LuUser, LuCreditCard, LuPackage, LuPower } from "react-icons/lu";
import { ScrollArea } from "@/app/components/ui/scroll-area";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetTitle } from "@/app/components/ui/sheet";
import { useEffect, useState } from "react";
import { MobileBottomNavigation } from "@/app/components/shared/mobile-bottom-navigation";
import { NotificationBadge } from "@/app/components/shared/notification/notification-badge";
import { Bell } from "lucide-react";
import { ThemeToggle } from "@/app/components/ui/theme-toggle";

interface UserLayoutProps {
  children: React.ReactNode;
}

interface UserData {
  id?: string;
  name?: string;
  email?: string;
  image?: string | null;
}

const navigation = [
  { 
    name: "Dashboard", 
    href: "/user/dashboard", 
    icon: LuDashboard,
    description: "Ikhtisar dan statistik penyewaan"
  },
  { 
    name: "Katalog", 
    href: "/user/catalog", 
    icon: LuPackage,
    description: "Lihat produk dan peralatan tersedia",
    highlight: true
  },
  { 
    name: "Rental Saya", 
    href: "/user/rentals", 
    icon: LuShoppingCart,
    description: "Kelola penyewaan aktif dan riwayat" 
  },
  { 
    name: "Status Operasi", 
    href: "/user/operations", 
    icon: LuPower,
    description: "Pantau status genset yang beroperasi" 
  },
  { 
    name: "Pembayaran", 
    href: "/user/payments", 
    icon: LuCreditCard,
    description: "Invoice dan riwayat pembayaran" 
  },
  { 
    name: "Profil", 
    href: "/user/profile", 
    icon: LuUser,
    description: "Pengaturan akun dan preferensi" 
  },
];

export default function UserLayout({ children }: UserLayoutProps) {
  const pathname = usePathname();
  const [user, setUser] = useState<UserData | null>(null);
  const [fetchError, setFetchError] = useState(false);
  
  useEffect(() => {
    // Fetch user data from server
    const fetchUserData = async () => {
      try {
        // Use relative URL to automatically handle base path
        const controller = new AbortController();
        const signal = controller.signal;
        
        const response = await fetch('/api/users/me', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          signal
        }).catch(err => {
          console.error("Network error in fetchUserData:", err);
          setFetchError(true);
          return null;
        });
        
        if (response && response.ok) {
          const userData = await response.json();
          setUser(userData);
          setFetchError(false);
        } else if (response) {
          console.error("Error fetching user data, status:", response.status);
          setFetchError(true);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        setFetchError(true);
      }
    };
    
    try {
      fetchUserData();
    } catch (e) {
      console.error("Fatal error in fetchUserData:", e);
      setFetchError(true);
    }
    
    // Cleanup function
    return () => {
      // Nothing to clean up since we removed the timeout
    };
  }, []);

  // User profile component
  const UserProfile = () => (
    <div className="border-t dark:border-gray-700 pt-4 mt-4 px-4">
      <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
        <div className="flex items-center gap-3 mb-3">
          {user?.image ? (
            <div className="w-10 h-10 rounded-full overflow-hidden ring-2 ring-white dark:ring-gray-700 shadow-sm">
              <Image 
                src={user.image} 
                alt={user.name || "Profil Pengguna"} 
                width={40} 
                height={40}
                className="w-full h-full object-cover" 
              />
            </div>
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-violet-500 to-indigo-600 dark:from-violet-600 dark:to-indigo-700 flex items-center justify-center shadow-sm">
              <span className="text-white font-medium">
                {user?.name?.[0] || "U"}
              </span>
            </div>
          )}
          <div className="flex-1 truncate">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{user?.name || 'Pengguna'}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{user?.email || '<EMAIL>'}</p>
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <ThemeToggle />
          <div className="flex gap-1">
            {fetchError ? (
              <Button variant="ghost" size="icon" className="relative text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full w-8 h-8">
                <Bell className="h-4 w-4" />
              </Button>
            ) : (
              <NotificationBadge />
            )}
            <Link href="/user/profile">
              <Button variant="ghost" size="icon" className="text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full w-8 h-8">
                <LuUser className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden user-dashboard-container">
      {/* Sidebar untuk desktop */}
      <aside className="hidden md:flex md:w-72 md:flex-col md:fixed md:inset-y-0">
        <div className="flex flex-col flex-1 min-h-0 border-r bg-white shadow-sm dark:bg-gray-900 dark:border-gray-800">
          <div className="flex items-center h-16 px-6 border-b bg-gradient-to-r from-violet-600 to-indigo-500 text-white dark:from-violet-700 dark:to-indigo-600 dark:border-violet-500">
            <Link href="/user/dashboard" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-white bg-opacity-20 rounded-md flex items-center justify-center">
                <span className="text-xl font-bold">RG</span>
              </div>
              <span className="text-xl font-semibold">RentalGenset</span>
            </Link>
          </div>
          <ScrollArea className="flex-1 py-4">
            <div className="px-3 mb-6">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <input 
                  type="text" 
                  placeholder="Cari menu..." 
                  className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-sky-500 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200 dark:placeholder-gray-400"
                />
              </div>
            </div>
            <nav className="space-y-1 px-3">
              {navigation.map((item) => {
                const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200",
                      isActive
                        ? "bg-violet-50 text-violet-600 dark:bg-violet-900/30 dark:text-violet-400"
                        : "text-gray-700 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100"
                    )}
                  >
                    <div className={cn(
                        "mr-3 rounded-md p-2",
                        isActive ? "bg-violet-100 dark:bg-violet-900/50" : "bg-gray-100 dark:bg-gray-700"
                      )}
                    >
                      <item.icon
                        className={cn(
                          "h-5 w-5",
                          isActive ? "text-violet-600 dark:text-violet-400" : "text-gray-500 dark:text-gray-400"
                        )}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span>{item.name}</span>
                        {item.highlight && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-200">
                            New
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{item.description}</p>
                    </div>
                  </Link>
                );
              })}
            </nav>
          </ScrollArea>
          {user && <UserProfile />}
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="md:hidden fixed top-4 left-4 z-50 text-gray-700 dark:text-gray-300">
            <LuMenu className="h-6 w-6" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-64 border-r-0 bg-white dark:bg-gray-900 dark:border-gray-800">
          <SheetTitle className="sr-only">Menu Navigasi Pengguna</SheetTitle>
          <div className="flex flex-col min-h-screen">
            <div className="flex items-center h-16 px-6 border-b bg-gradient-to-r from-violet-600 to-indigo-500 text-white dark:from-violet-700 dark:to-indigo-600 dark:border-violet-500">
              <Link href="/user/dashboard" className="flex items-center">
                <span className="text-xl font-semibold">RentalGenset</span>
              </Link>
            </div>
            <ScrollArea className="flex-1 py-4">
              <nav className="space-y-1 px-2">
                {navigation.map((item) => {
                  const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "group flex items-center px-3 py-2 text-sm font-medium rounded-md",
                        isActive
                          ? "bg-violet-50 text-violet-600 dark:bg-violet-900/30 dark:text-violet-400"
                          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100"
                      )}
                    >
                      <item.icon
                        className={cn(
                          "mr-3 h-5 w-5 flex-shrink-0",
                          isActive ? "text-violet-600 dark:text-violet-400" : "text-gray-500 dark:text-gray-400"
                        )}
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
            </ScrollArea>
            {user && <UserProfile />}
          </div>
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="flex flex-col flex-1 md:pl-64">
        <header className="sticky top-0 z-10 flex items-center justify-between p-4 border-b bg-white dark:bg-gray-900 dark:border-gray-800 shadow-sm">
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="icon"
              className="md:hidden h-9 w-9 rounded-full border-gray-200 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-800"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Cek apakah ada halaman sebelumnya dalam history
                if (window.history.length > 1) {
                  window.history.back();
                } else {
                  // Jika tidak ada history, redirect ke dashboard
                  window.location.href = '/user/dashboard';
                }
              }}
              onTouchStart={(e) => {
                // Prevent accidental touches
                e.stopPropagation();
              }}
            >
              <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
                <path d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
              </svg>
            </Button>
            <div className="font-semibold text-lg dark:text-white">RentalGenset</div>
          </div>
          <div className="flex items-center gap-2 ml-auto">
            <ThemeToggle />
            {fetchError ? (
              <Button variant="ghost" size="icon" className="relative text-gray-700 dark:text-gray-300">
                <Bell className="h-5 w-5" />
              </Button>
            ) : (
              <NotificationBadge />
            )}
          </div>
        </header>
        <main className="flex-1 overflow-y-auto pb-24 md:pb-10" style={{ touchAction: 'pan-y pinch-zoom' }}>
          <div className="px-4 sm:px-6 lg:px-8 py-6 max-w-7xl mx-auto">
            {children}
          </div>
        </main>

        {/* Mobile Bottom Navigation */}
        <MobileBottomNavigation />
      </div>
    </div>
  );
} 
