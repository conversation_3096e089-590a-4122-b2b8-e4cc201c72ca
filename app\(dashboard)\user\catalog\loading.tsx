export default function CatalogLoading() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div className="h-8 w-48 bg-gray-200 rounded mb-6 animate-pulse" />
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="aspect-video bg-gray-200 animate-pulse" />
            
            <div className="p-6 space-y-4">
              <div className="h-6 w-3/4 bg-gray-200 rounded animate-pulse" />
              
              <div className="space-y-2">
                <div className="h-4 w-2/3 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse" />
              </div>
              
              <div className="h-6 w-1/3 bg-gray-200 rounded animate-pulse" />
              
              <div className="h-10 w-full bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
