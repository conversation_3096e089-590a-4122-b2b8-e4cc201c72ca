export default function RentalLoading() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div className="h-8 bg-gray-200 rounded w-48 mb-6 animate-pulse"></div>
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse flex justify-between">
              <div className="space-y-3 flex-1">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              </div>
              <div className="space-y-3 text-right">
                <div className="h-4 bg-gray-200 rounded w-24 ml-auto"></div>
                <div className="h-4 bg-gray-200 rounded w-20 ml-auto"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 
