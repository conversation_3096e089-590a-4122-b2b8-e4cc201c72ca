import { Metada<PERSON> } from "next";
import Link from "next/link";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { getStats } from "@/lib/data/dashboard";
import { DashboardStats } from "@/lib/types/dashboard";
import { RevenueBarChart } from "@/app/components/ui/revenue-bar-chart";
import {
  Card, 
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { LuArrowRight, LuTrendingUp, LuTrendingDown, LuDollarSign, LuShoppingBag, LuPercent } from "react-icons/lu";
import DashboardWrapper from "@/app/components/dashboard/dashboard-wrapper";
import { DashboardNotifications } from "@/app/components/admin/dashboard-notifications";

export const metadata: Metadata = {
  title: "Dashboard Admin",
  description: "Dashboard administrasi Rental Genset",
};

export default async function AdminDashboardPage() {
  const session = await auth();

  // Redirect jika pengguna tidak login atau bukan admin
  if (!session?.user) {
    redirect('/login');
  }
  if (session.user.role !== "ADMIN") {
    redirect('/user/dashboard');
  }

  // Log session info untuk debugging
  console.log("Dashboard session user:", {
    id: session.user.id,
    role: session.user.role,
    name: session.user.name
  });

  // Akses API langsung daripada melalui fungsi getStats
  try {
    const stats: DashboardStats = await getStats();

    return (
      <>
        <div className="flex items-center justify-between space-y-2 mb-8">
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <div className="flex items-center space-x-2">
            <Button>Download Laporan</Button>
          </div>
        </div>

        <DashboardWrapper>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="border dark:border-gray-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Pendapatan
                </CardTitle>
                <LuDollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    maximumFractionDigits: 0
                  }).format(stats.totalRevenue)}
                </div>
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  {stats.revenueGrowth > 0 ? (
                    <>
                      <LuTrendingUp className="mr-1 text-blue-500" />
                      <span className="text-blue-500">{stats.revenueGrowth}%</span>
                    </>
                  ) : (
                    <>
                      <LuTrendingDown className="mr-1 text-red-500" />
                      <span className="text-red-500">{Math.abs(stats.revenueGrowth)}%</span>
                    </>
                  )}
                  <span className="ml-1">dibanding bulan lalu</span>
                </p>
              </CardContent>
            </Card>
            <Card className="border dark:border-gray-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Rental
                </CardTitle>
                <LuShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalRentals}</div>
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  {stats.rentalGrowth > 0 ? (
                    <>
                      <LuTrendingUp className="mr-1 text-blue-500" />
                      <span className="text-blue-500">{stats.rentalGrowth}%</span>
                    </>
                  ) : (
                    <>
                      <LuTrendingDown className="mr-1 text-red-500" />
                      <span className="text-red-500">{Math.abs(stats.rentalGrowth)}%</span>
                    </>
                  )}
                  <span className="ml-1">dibanding bulan lalu</span>
                </p>
              </CardContent>
            </Card>
            <Card className="border dark:border-gray-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Produk
                </CardTitle>
                <LuShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalProducts}</div>
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  {stats.productGrowth > 0 ? (
                    <>
                      <LuTrendingUp className="mr-1 text-blue-500" />
                      <span className="text-blue-500">{stats.productGrowth}%</span>
                    </>
                  ) : (
                    <>
                      <LuTrendingDown className="mr-1 text-red-500" />
                      <span className="text-red-500">{Math.abs(stats.productGrowth)}%</span>
                    </>
                  )}
                  <span className="ml-1">dibanding bulan lalu</span>
                </p>
              </CardContent>
            </Card>
            <Card className="border dark:border-gray-800">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Tingkat Hunian
                </CardTitle>
                <LuPercent className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.occupancyRate}%</div>
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  {stats.occupancyGrowth > 0 ? (
                    <>
                      <LuTrendingUp className="mr-1 text-blue-500" />
                      <span className="text-blue-500">{stats.occupancyGrowth}%</span>
                    </>
                  ) : (
                    <>
                      <LuTrendingDown className="mr-1 text-red-500" />
                      <span className="text-red-500">{Math.abs(stats.occupancyGrowth)}%</span>
                    </>
                  )}
                  <span className="ml-1">dibanding bulan lalu</span>
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7 mt-8">
            <Card className="col-span-4 border dark:border-gray-800">
              <CardHeader>
                <CardTitle>Pendapatan Bulanan</CardTitle>
                <CardDescription>
                  Pendapatan dari penyewaan genset dalam 12 bulan terakhir
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RevenueBarChart data={stats.monthlyRevenue} />
              </CardContent>
            </Card>
            <Card className="col-span-3 border dark:border-gray-800">
              <CardHeader>
                <CardTitle>Rental Terbaru</CardTitle>
                <CardDescription>
                  Daftar 5 transaksi rental terakhir
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {stats.recentRentals.map((rental) => (
                    <div className="flex items-center" key={rental.id}>
                      <div className="space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {rental.customerName}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {rental.productName}
                        </p>
                      </div>
                      <div className="ml-auto font-medium">
                        {new Intl.NumberFormat('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          maximumFractionDigits: 0
                        }).format(rental.amount)}
                      </div>
                      <div className="ml-4">
                        <Badge variant={
                          rental.status === "COMPLETED" ? "default" :
                            rental.status === "ACTIVE" ? "secondary" :
                              "outline"
                        }>
                          {rental.status === "COMPLETED" ? "Selesai" :
                            rental.status === "ACTIVE" ? "Aktif" :
                              rental.status === "PENDING" ? "Tertunda" :
                                rental.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <Link href="/admin/rentals" className="mx-6 mb-6 text-sm text-blue-600 hover:text-blue-700 flex items-center">
                Lihat semua rental
                <LuArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-8">
            <div className="col-span-4">
              <DashboardNotifications />
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-8">
            <div className="col-span-4">
              <Card className="border dark:border-gray-800">
                <CardHeader>
                  <CardTitle>Produk Teratas</CardTitle>
                  <CardDescription>
                    Produk paling banyak disewa
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats.topProducts?.map((product) => (
                      <div className="flex items-center" key={product.id}>
                        <div className="space-y-1">
                          <p className="text-sm font-medium leading-none">
                            {product.name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {product.capacity} KVA
                          </p>
                        </div>
                        <div className="ml-auto font-medium">
                          {product.rentalCount} kali
                        </div>
                      </div>
                    )) || (
                        <div className="py-4 text-center text-gray-500">
                          Belum ada data produk teratas
                        </div>
                      )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </DashboardWrapper>
      </>
    );
  } catch (error) {
    console.error("Failed to render dashboard:", error);
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h2 className="text-lg font-bold text-red-700 mb-2">Gagal Memuat Dashboard</h2>
        <p className="text-red-600 mb-4">
          Terjadi kesalahan saat memuat data dashboard. Silakan coba lagi nanti.
        </p>
      </div>
    );
  }
}
