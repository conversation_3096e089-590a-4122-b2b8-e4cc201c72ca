/**
 * Utilitas untuk optimasi performa
 * File ini berisi fungsi-fungsi dan hook yang membantu meningkatkan performa aplikasi
 */

import { useEffect, useState, useCallback, useMemo, DependencyList } from "react";
import React from "react";

/**
 * Hook untuk debouncing nilai input
 * Berguna untuk input yang sering berubah seperti search field
 * 
 * @param value Nilai yang ingin di-debounce
 * @param delay Waktu delay dalam milidetik
 * @returns Nilai yang sudah di-debounce
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Hook untuk lazy loading data
 * Berguna untuk memuat data hanya ketika komponen terlihat di viewport
 * 
 * @param callback Fungsi untuk memuat data
 * @returns [data, loading, error, ref]
 */
export function useLazyLoad<T>(callback: () => Promise<T>) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [ref, setRef] = useState<Element | null>(null);

  const observer = useMemo(
    () =>
      typeof window !== "undefined"
        ? new IntersectionObserver(entries => {
            entries.forEach(entry => {
              if (entry.isIntersecting && !loading && !data) {
                setLoading(true);
                callback()
                  .then(result => {
                    setData(result);
                    setLoading(false);
                  })
                  .catch(err => {
                    setError(err);
                    setLoading(false);
                  });
              }
            });
          })
        : null,
    [callback, data, loading]
  );

  useEffect(() => {
    if (ref && observer) {
      observer.observe(ref);
      return () => {
        observer.unobserve(ref);
      };
    }
  }, [ref, observer]);

  return [data, loading, error, setRef];
}

/**
 * Memoize fungsi dengan cache untuk input yang sama
 * 
 * @param fn Fungsi yang ingin di-memoize
 * @returns Fungsi yang sudah di-memoize
 */
export function memoize<T extends (...args: unknown[]) => unknown>(fn: T): T {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args);
    const cachedResult = cache.get(key);
    if (cache.has(key) && cachedResult !== undefined) {
      return cachedResult;
    }
    
    const result = fn(...args) as ReturnType<T>;
    cache.set(key, result);
    return result;
  }) as T;
}

/**
 * Hook useMemoAsync untuk meng-cache hasil fungsi async
 * 
 * @param asyncFn Fungsi async yang ingin di-cache
 * @param deps Dependencies untuk useCallback
 * @returns [data, loading, error, refresh]
 */
export function useMemoAsync<T>(
  asyncFn: () => Promise<T>,
  deps: DependencyList = []
): [T | null, boolean, Error | null, () => void] {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Gunakan useRef untuk menyimpan asyncFn terbaru
  const asyncFnRef = React.useRef(asyncFn);
  
  // Update ref setiap kali asyncFn berubah
  React.useEffect(() => {
    asyncFnRef.current = asyncFn;
  }, [asyncFn]);

  const execute = useCallback(() => {
    setLoading(true);
    setError(null);
    
    return asyncFnRef.current()
      .then(response => {
        setData(response);
        setLoading(false);
        return response;
      })
      .catch(err => {
        setError(err);
        setLoading(false);
        throw err;
      });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps); // Gunakan deps langsung tanpa spread

  useEffect(() => {
    execute().catch(() => {
      // Error sudah ditangani di execute
    });
  }, [execute]);

  return [data, loading, error, execute];
}

/**
 * Optimasi untuk daftar panjang dengan windowing
 * Hanya render item yang terlihat di viewport
 * 
 * @param items Array item yang ingin di-render
 * @param itemHeight Tinggi setiap item dalam pixel
 * @param containerHeight Tinggi container dalam pixel
 * @param overscan Jumlah item tambahan yang di-render di atas/bawah viewport
 * @returns Item yang harus di-render
 */
export function useVirtualizedList<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number, 
  overscan: number = 3
) {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleStartIndex = Math.floor(scrollTop / itemHeight);
  const visibleEndIndex = Math.min(
    items.length - 1,
    Math.floor((scrollTop + containerHeight) / itemHeight)
  );
  
  const startIndex = Math.max(0, visibleStartIndex - overscan);
  const endIndex = Math.min(items.length - 1, visibleEndIndex + overscan);
  
  const visibleItems = useMemo(() => {
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
      style: {
        position: 'absolute',
        top: (startIndex + index) * itemHeight,
        height: itemHeight,
      },
    }));
  }, [items, startIndex, endIndex, itemHeight]);
  
  const onScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);
  
  return {
    visibleItems,
    totalHeight: items.length * itemHeight,
    onScroll
  };
} 
