import { NextResponse } from 'next/server';
import { EmailService } from '@/lib/services/email';

export async function GET() {
  try {
    // Check email service configuration
    const config = {
      smtp: {
        user: (process.env.EMAIL_USER || process.env.SMTP_USER) ? 'configured' : 'missing',
        pass: (process.env.EMAIL_PASS || process.env.SMTP_PASS) ? 'configured' : 'missing',
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: process.env.SMTP_PORT || '587',
        from: process.env.EMAIL_FROM || 'not configured'
      },
      resend: {
        apiKey: process.env.RESEND_API_KEY ? 'configured' : 'missing'
      }
    };

    // Determine which email service is available
    let availableServices = [];
    if (config.resend.apiKey === 'configured') {
      availableServices.push('Resend');
    }
    if (config.smtp.user === 'configured' && config.smtp.pass === 'configured') {
      availableServices.push('SMTP');
    }

    const status = {
      configured: availableServices.length > 0,
      availableServices,
      config,
      recommendations: []
    };

    // Add recommendations
    if (availableServices.length === 0) {
      status.recommendations.push('No email service configured. Please set up either Resend API key or SMTP credentials.');
    } else if (availableServices.length === 1) {
      status.recommendations.push('Consider setting up a backup email service for redundancy.');
    }

    if (config.smtp.from === 'not configured') {
      status.recommendations.push('Set EMAIL_FROM environment variable for better email delivery.');
    }

    return NextResponse.json({
      success: true,
      status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Email status check error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST() {
  try {
    // Perform a live email test
    const testResult = await EmailService.testConnection();
    
    return NextResponse.json({
      success: testResult.success,
      testResult,
      timestamp: new Date().toISOString(),
      message: testResult.success 
        ? `Email test successful via ${testResult.method}` 
        : `Email test failed: ${testResult.error}`
    });

  } catch (error) {
    console.error('Email test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
