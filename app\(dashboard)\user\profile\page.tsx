import { Metada<PERSON> } from "next";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { ToasterProvider } from "./toaster-provider";
import { updateProfile, updatePassword } from "./actions";
import { ProfileClient } from "./profile-client";

export const metadata: Metadata = {
  title: "Profil Saya",
  description: "Kelola profil pengguna Anda",
};

export default async function ProfilePage() {
  const session = await auth();
  if (!session?.user) {
    redirect('/login');
  }

  // Gunakan data yang tersedia dari session dan beri default value untuk yang tidak ada
  const user = {
    id: session.user.id || "",
    name: session.user.name || "Pengguna",
    email: session.user.email || "-",
    phone: session.user.phone || "-",
    image: session.user.image || "/images/avatar-placeholder.png",
    role: session.user.role || "USER",
    // Data tambahan yang tidak ada di session
    address: "",
    bio: ""
  };

  // Format tanggal bergabung dengan nilai default
  let joinDate = "Hari ini";
  
  // Gunakan try-catch untuk menangani potensi error
  try {
    if (session.user.createdAt) {
      const date = new Date(session.user.createdAt);
      joinDate = date.toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    }
  } catch (error) {
    console.error("Error formatting join date:", error);
    // Gunakan default jika gagal memformat
  }

  const handleProfileUpdate = async (formData: FormData) => {
    "use server";
    
    try {
      // Jalankan fungsi updateProfile untuk mengupdate profil
      const result = await updateProfile(formData);
      
      // Log hasil untuk debug
      console.log("Profile update result:", result);
    } catch (error) {
      console.error("Error updating profile:", error);
    }
  };

  const handlePasswordUpdate = async (formData: FormData) => {
    "use server";
    try {
      await updatePassword(formData);
    } catch (error) {
      console.error("Error updating password:", error);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <ToasterProvider />
      
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-gradient-to-r from-violet-200 to-indigo-300 dark:from-violet-800 dark:to-indigo-700"></div>
        <div className="relative">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Profil Saya</h1>
          <p className="text-gray-600 dark:text-gray-400 max-w-xl mt-2">
            Kelola informasi profil dan akun Anda
          </p>
        </div>
      </div>

      {/* Gunakan komponen client baru untuk menampilkan dan mengupdate profil */}
      <ProfileClient 
        user={{
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          image: user.image,
          role: user.role,
          address: user.address,
          bio: user.bio,
          joinDate: joinDate
        }}
        handleProfileUpdate={handleProfileUpdate}
        handlePasswordUpdate={handlePasswordUpdate}
      />
    </div>
  );
}
