"use client";

import { useEffect, useRef, useState, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { MapDisplayProps } from '@/lib/types/map';
import { useReverseGeocode } from '@/lib/hooks/useReverseGeocode';

export function MapDisplay({
  latitude,
  longitude,
  zoom = 12,
  markerDraggable = false,
  onMarkerDrag,
  className = ""
}: MapDisplayProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<maplibregl.Map | null>(null);
  const marker = useRef<maplibregl.Marker | null>(null);
  const isDragging = useRef<boolean>(false);
  const lastPosition = useRef<{lat: number, lng: number}>({lat: latitude, lng: longitude});
  const geocodeDebounceTimer = useRef<NodeJS.Timeout | null>(null);

  const [mapLoaded, setMapLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const { getAddressFromCoordinates } = useReverseGeocode();

  // Fungsi untuk mendapatkan alamat dengan debounce
  const getAddressWithDebounce = useCallback((lat: number, lng: number): Promise<string> => {
    return new Promise((resolve) => {
      // Batalkan timer sebelumnya jika ada
      if (geocodeDebounceTimer.current) {
        clearTimeout(geocodeDebounceTimer.current);
      }

      // Set timer baru untuk debounce
      geocodeDebounceTimer.current = setTimeout(async () => {
        try {
          const address = await getAddressFromCoordinates(lat, lng);
          resolve(address);
        } catch (error) {
          console.error("Error getting address:", error);
          resolve("Alamat tidak ditemukan");
        }
      }, 300); // 300ms debounce
    });
  }, [getAddressFromCoordinates]);

  // Fungsi untuk memindahkan marker tanpa membuat marker baru
  const moveMarker = useCallback((lnglat: [number, number]) => {
    if (marker.current && map.current) {
      console.log("Memindahkan marker ke:", lnglat);
      marker.current.setLngLat(lnglat);
      lastPosition.current = {lat: lnglat[1], lng: lnglat[0]};
    }
  }, []);

  // Fungsi untuk memastikan marker berada di tengah peta
  const centerMarkerOnMap = useCallback(() => {
    if (map.current && marker.current) {
      const center = map.current.getCenter();
      const markerPos = marker.current.getLngLat();

      // Jika marker tidak di tengah, pindahkan ke tengah
      const centerLng = center.lng;
      const centerLat = center.lat;
      const markerLng = markerPos.lng;
      const markerLat = markerPos.lat;

      const lngDiff = Math.abs(centerLng - markerLng);
      const latDiff = Math.abs(centerLat - markerLat);

      if (lngDiff > 0.001 || latDiff > 0.001) {
        console.log("Menyelaraskan marker ke tengah peta");
        moveMarker([centerLng, centerLat]);
        lastPosition.current = {lat: centerLat, lng: centerLng};
      }
    }
  }, [moveMarker]);

  // Fungsi untuk membuat marker baru dan menghapus marker lama
  const createNewMarker = useCallback((lnglat: [number, number]) => {
    // Hapus marker lama jika ada
    if (marker.current) {
      marker.current.remove();
      marker.current = null;
    }

    // Buat marker baru jika peta ada
    if (map.current) {
      // Simpan posisi terakhir
      lastPosition.current = {lat: lnglat[1], lng: lnglat[0]};

      // Buat elemen marker custom dengan desain yang lebih baik untuk mobile
      const markerEl = document.createElement('div');
      markerEl.className = 'custom-marker';
      markerEl.style.cssText = `
        position: relative;
        width: 40px;
        height: 50px;
        cursor: pointer;
        filter: drop-shadow(0 4px 8px rgba(0,0,0,0.25));
      `;

      markerEl.innerHTML = `
        <div class="marker-pin" style="
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 40px;
          height: 50px;
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          border: 4px solid white;
          border-radius: 50% 50% 50% 0;
          transform: translateX(-50%) rotate(-45deg);
          transform-origin: center bottom;
          box-shadow:
            0 0 0 2px rgba(239, 68, 68, 0.2),
            0 4px 12px rgba(0,0,0,0.15),
            inset 0 1px 0 rgba(255,255,255,0.2);
          display: flex;
          justify-content: center;
          align-items: center;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        ">
          <div class="marker-icon" style="
            transform: rotate(45deg);
            color: white;
            font-weight: bold;
            font-size: 14px;
            margin-top: -4px;
            margin-left: -1px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
          ">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="filter: drop-shadow(0 1px 1px rgba(0,0,0,0.2));">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
            </svg>
          </div>

          <!-- Pulse animation ring untuk visibility yang lebih baik -->
          <div class="marker-pulse" style="
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            background: rgba(239, 68, 68, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
            animation: pulse 2s infinite;
            pointer-events: none;
          "></div>
        </div>

        <style>
          @keyframes pulse {
            0% {
              transform: translate(-50%, -50%) rotate(45deg) scale(0.8);
              opacity: 1;
            }
            70% {
              transform: translate(-50%, -50%) rotate(45deg) scale(1.2);
              opacity: 0;
            }
            100% {
              transform: translate(-50%, -50%) rotate(45deg) scale(1.2);
              opacity: 0;
            }
          }

          .custom-marker:hover .marker-pin {
            transform: translateX(-50%) rotate(-45deg) scale(1.1);
            box-shadow:
              0 0 0 3px rgba(239, 68, 68, 0.3),
              0 6px 16px rgba(0,0,0,0.2),
              inset 0 1px 0 rgba(255,255,255,0.3);
          }

          .custom-marker:active .marker-pin {
            transform: translateX(-50%) rotate(-45deg) scale(0.95);
          }
        </style>
      `;

      // Tambahkan touch dan hover effects untuk marker yang responsif
      markerEl.addEventListener('mouseenter', () => {
        const pin = markerEl.querySelector('.marker-pin') as HTMLElement;
        const pulse = markerEl.querySelector('.marker-pulse') as HTMLElement;
        if (pin) {
          pin.style.transform = 'translateX(-50%) rotate(-45deg) scale(1.1)';
          pin.style.boxShadow = `
            0 0 0 3px rgba(239, 68, 68, 0.3),
            0 6px 16px rgba(0,0,0,0.2),
            inset 0 1px 0 rgba(255,255,255,0.3)
          `;
        }
        if (pulse) {
          pulse.style.animationDuration = '1s';
        }
      });

      markerEl.addEventListener('mouseleave', () => {
        const pin = markerEl.querySelector('.marker-pin') as HTMLElement;
        const pulse = markerEl.querySelector('.marker-pulse') as HTMLElement;
        if (pin) {
          pin.style.transform = 'translateX(-50%) rotate(-45deg) scale(1)';
          pin.style.boxShadow = `
            0 0 0 2px rgba(239, 68, 68, 0.2),
            0 4px 12px rgba(0,0,0,0.15),
            inset 0 1px 0 rgba(255,255,255,0.2)
          `;
        }
        if (pulse) {
          pulse.style.animationDuration = '2s';
        }
      });

      // Touch events untuk mobile
      markerEl.addEventListener('touchstart', (e) => {
        e.preventDefault();
        const pin = markerEl.querySelector('.marker-pin') as HTMLElement;
        if (pin) {
          pin.style.transform = 'translateX(-50%) rotate(-45deg) scale(0.95)';
        }
      });

      markerEl.addEventListener('touchend', (e) => {
        e.preventDefault();
        const pin = markerEl.querySelector('.marker-pin') as HTMLElement;
        if (pin) {
          pin.style.transform = 'translateX(-50%) rotate(-45deg) scale(1)';
        }
      });

      // Buat marker dengan elemen kustom dan anchor yang tepat
      marker.current = new maplibregl.Marker({
        element: markerEl,
        draggable: markerDraggable,
        anchor: 'bottom'
      })
        .setLngLat(lnglat)
        .addTo(map.current);

      // Register event handler untuk marker baru jika bisa di-drag
      if (markerDraggable && onMarkerDrag) {
        // Tambahkan event listener untuk drag
        marker.current.on('dragstart', () => {
          isDragging.current = true;
        });

        marker.current.on('drag', () => {
          // Saat dragging, update posisi secara real-time
          if (!marker.current) return;
          const currentPos = marker.current.getLngLat();
          lastPosition.current = {lat: currentPos.lat, lng: currentPos.lng};
        });

        marker.current.on('dragend', async () => {
          if (!marker.current) return;

          try {
            // Dapatkan posisi akhir dari marker
            const markerLngLat = marker.current.getLngLat();

            // Dapatkan alamat dengan debounce untuk menghindari permintaan berlebihan
            const newAddress = await getAddressWithDebounce(markerLngLat.lat, markerLngLat.lng);

            // Kirim callback ke parent component
            if (onMarkerDrag) {
              onMarkerDrag({
                lat: markerLngLat.lat,
                lng: markerLngLat.lng,
                address: newAddress
              });
            }
          } catch (error) {
            console.error("Error during drag processing:", error);
          } finally {
            // Delay reset status drag untuk memastikan proses rendering selesai
            setTimeout(() => {
              isDragging.current = false;
            }, 500);
          }
        });
      }

      // Tambahkan event listener untuk klik marker
      marker.current.getElement().addEventListener('click', async () => {
        if (!marker.current || isDragging.current) return;

        const markerLngLat = marker.current.getLngLat();
        isDragging.current = true;

        try {
          // Dapatkan alamat dengan debounce
          const newAddress = await getAddressWithDebounce(markerLngLat.lat, markerLngLat.lng);

          // Kirim callback ke parent dengan lokasi yang dipilih
          if (onMarkerDrag) {
            onMarkerDrag({
              lat: markerLngLat.lat,
              lng: markerLngLat.lng,
              address: newAddress
            });
          }
        } catch (error) {
          console.error("Error processing marker click:", error);
        } finally {
          setTimeout(() => {
            isDragging.current = false;
          }, 500);
        }
      });
    }
  }, [markerDraggable, onMarkerDrag, getAddressWithDebounce]);

  // Inisialisasi peta MapLibre
  useEffect(() => {
    if (!mapContainer.current) return;

    // Reset state jika map sudah ada sebelumnya
    if (map.current) {
      map.current.remove();
      map.current = null;
    }

    // Reset marker
    if (marker.current) {
      marker.current.remove();
      marker.current = null;
    }

    setIsLoading(true);

    try {
      // Buat peta dengan rendering optimasi dan konfigurasi mobile-friendly
      map.current = new maplibregl.Map({
        container: mapContainer.current,
        style: {
          version: 8,
          sources: {
            'osm': {
              type: 'raster',
              tiles: [
                'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
                'https://b.tile.openstreetmap.org/{z}/{x}/{y}.png',
                'https://c.tile.openstreetmap.org/{z}/{x}/{y}.png'
              ],
              tileSize: 256,
              attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
              maxzoom: 19
            }
          },
          layers: [
            {
              id: 'osm-tiles',
              type: 'raster',
              source: 'osm',
              minzoom: 0,
              maxzoom: 19
            }
          ]
        },
        center: [longitude, latitude],
        zoom: zoom,
        renderWorldCopies: true,
        attributionControl: false, // Sembunyikan atribusi untuk peningkatan performa

        // Optimasi untuk mobile dan touch devices
        touchZoomRotate: true, // Enable touch zoom dan rotate
        touchPitch: false, // Disable pitch untuk kemudahan navigasi mobile
        dragRotate: false, // Disable rotation untuk kemudahan navigasi mobile
        keyboard: false, // Disable keyboard navigation
        doubleClickZoom: true, // Enable double click zoom
        scrollZoom: true, // Enable scroll zoom
        boxZoom: false, // Disable box zoom untuk mobile
        dragPan: true, // Enable drag pan

        // Batas zoom untuk performa yang lebih baik
        minZoom: 8,
        maxZoom: 20
      });

      // Tambahkan kontrol navigasi yang dioptimasi untuk mobile
      const navControl = new maplibregl.NavigationControl({
        showCompass: false, // Sembunyikan compass untuk mobile
        showZoom: true, // Tampilkan zoom controls
        visualizePitch: false // Disable pitch visualization
      });

      map.current.addControl(navControl, 'top-right');

      // Styling khusus untuk kontrol navigasi mobile
      setTimeout(() => {
        const navControlElement = document.querySelector('.maplibregl-ctrl-group');
        if (navControlElement) {
          (navControlElement as HTMLElement).style.cssText = `
            margin: 10px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
            border-radius: 8px !important;
            overflow: hidden !important;
          `;

          // Perbesar tombol zoom untuk mobile
          const zoomButtons = navControlElement.querySelectorAll('button');
          zoomButtons.forEach((button) => {
            (button as HTMLElement).style.cssText = `
              width: 44px !important;
              height: 44px !important;
              font-size: 18px !important;
              line-height: 44px !important;
              border: none !important;
              background: white !important;
              color: #374151 !important;
              cursor: pointer !important;
              transition: all 0.2s ease !important;
            `;

            // Hover effects untuk desktop
            button.addEventListener('mouseenter', () => {
              (button as HTMLElement).style.backgroundColor = '#f3f4f6';
            });

            button.addEventListener('mouseleave', () => {
              (button as HTMLElement).style.backgroundColor = 'white';
            });

            // Touch feedback untuk mobile
            button.addEventListener('touchstart', () => {
              (button as HTMLElement).style.backgroundColor = '#e5e7eb';
              (button as HTMLElement).style.transform = 'scale(0.95)';
            });

            button.addEventListener('touchend', () => {
              (button as HTMLElement).style.backgroundColor = 'white';
              (button as HTMLElement).style.transform = 'scale(1)';
            });
          });
        }
      }, 100);

      // Kurangi beban rendering selama interaksi
      map.current.on('movestart', () => {
        if (map.current && !isDragging.current) {
          map.current.getCanvas().style.imageRendering = 'optimizeSpeed';
        }
      });

      map.current.on('moveend', () => {
        if (map.current) {
          map.current.getCanvas().style.imageRendering = 'auto';

          // Pastikan marker tetap di tengah setelah peta berhenti bergerak
          // Hanya jika tidak sedang dalam proses drag marker
          if (!isDragging.current && mapLoaded) {
            setTimeout(() => {
              centerMarkerOnMap();
            }, 50);
          }
        }
      });

      // Error handling untuk tile loading
      map.current.on('error', (e) => {
        console.warn("Map error occurred:", e);
        // Jangan set error state, biarkan peta tetap mencoba memuat
      });

      // Error handling untuk source data
      map.current.on('sourcedataloading', (e) => {
        if (e.sourceId === 'osm') {
          console.log("Loading OSM tiles...");
        }
      });

      map.current.on('sourcedata', (e) => {
        if (e.sourceId === 'osm' && e.isSourceLoaded) {
          console.log("OSM tiles loaded successfully");
        }
      });

      // Set event ketika peta selesai dimuat
      map.current.on('load', () => {
        // Pastikan map.current masih ada
        if (!map.current) return;

        console.log("Peta selesai dimuat, menambahkan marker di:", longitude, latitude);

        // Tunggu sebentar untuk memastikan peta benar-benar siap
        setTimeout(() => {
          if (map.current) {
            // Pastikan peta berada di koordinat yang tepat
            map.current.setCenter([longitude, latitude]);
            map.current.setZoom(zoom);

            // Tambahkan marker saat peta sudah dimuat dan siap
            createNewMarker([longitude, latitude]);

            setMapLoaded(true);
            setIsLoading(false);
          }
        }, 100);
      });
    } catch (error) {
      console.error("Error initializing map:", error);
      setIsLoading(false);
    }

    // Cleanup saat komponen di-unmount
    return () => {
      if (geocodeDebounceTimer.current) {
        clearTimeout(geocodeDebounceTimer.current);
      }

      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, [latitude, longitude, zoom, createNewMarker, centerMarkerOnMap, mapLoaded]);

  // Update marker dan posisi peta jika koordinat berubah setelah peta dimuat
  useEffect(() => {
    // Jika sedang dalam proses drag atau peta belum dimuat, jangan update
    if (isDragging.current || !mapLoaded || !map.current) return;

    // Cek apakah posisi baru berbeda dari posisi terakhir yang diketahui
    const latDiff = Math.abs(lastPosition.current.lat - latitude);
    const lngDiff = Math.abs(lastPosition.current.lng - longitude);

    // Hanya update jika perbedaan koordinat signifikan (lebih dari 0.0001 derajat)
    if (latDiff > 0.0001 || lngDiff > 0.0001) {
      console.log("Mengupdate posisi marker dari", lastPosition.current, "ke", {lat: latitude, lng: longitude});

      // Update posisi terakhir
      lastPosition.current = { lat: latitude, lng: longitude };

      // Fly to new location dengan animasi yang lebih halus
      map.current.flyTo({
        center: [longitude, latitude],
        zoom: zoom,
        essential: true,
        duration: 1200, // Durasi animasi yang optimal
        curve: 1.42, // Gunakan kurva easing untuk animasi yang lebih natural
        easing: (t) => t * (2 - t) // Fungsi easing custom untuk animasi yang lebih smooth
      });

      // Tunggu animasi selesai sebelum mengupdate marker
      setTimeout(() => {
        // Gunakan moveMarker daripada membuat marker baru jika marker sudah ada
        if (marker.current) {
          moveMarker([longitude, latitude]);
        } else {
          createNewMarker([longitude, latitude]);
        }
      }, 100);
    }
  }, [latitude, longitude, zoom, mapLoaded, createNewMarker, moveMarker]);

  return (
    <div className={`relative ${className}`}>
      <div
        ref={mapContainer}
        className="h-full w-full rounded-lg overflow-hidden touch-pan-y touch-pinch-zoom"
        style={{
          minHeight: '280px', // Lebih kecil untuk mobile
          willChange: 'transform', // Optimasi rendering
          backfaceVisibility: 'hidden', // Meningkatkan performa rendering
          WebkitOverflowScrolling: 'touch', // Smooth scrolling di iOS
          touchAction: 'pan-x pan-y' // Optimasi touch untuk mobile
        }}
      />
      {isLoading && (
        <div className="absolute inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 flex items-center justify-center rounded-lg">
          <div className="flex flex-col items-center p-4">
            <div className="w-8 h-8 sm:w-10 sm:h-10 border-4 border-t-red-500 border-gray-200 dark:border-gray-600 rounded-full animate-spin" />
            <p className="mt-3 text-sm sm:text-base text-gray-700 dark:text-gray-300 font-medium">
              Memuat peta...
            </p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Mohon tunggu sebentar
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

export default MapDisplay;
