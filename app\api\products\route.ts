import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/config/prisma';
import { auth } from "@/auth";
import { ProductSchema } from "@/lib/validations/product/schema";
import { revalidatePath } from "next/cache";

export async function GET(request: NextRequest) {
  try {
    // Ambil query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const capacity = searchParams.get('capacity');
    const minPrice = parseInt(searchParams.get('minPrice') || '0');
    const maxPrice = parseInt(searchParams.get('maxPrice') || '1000000000');
    const availability = searchParams.get('availability');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '9');
    const offset = (page - 1) * limit;

    // Buat where query dengan conditions
    // Menggunakan Record<string, unknown> untuk menghindari 'any' tapi tetap memungkinkan struktur dinamis
    const where: Record<string, unknown> = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (category) {
      where.category = category;
    }
    
    if (capacity) {
      // Coba parse capacity sebagai angka jika memungkinkan
      const numCapacity = parseInt(capacity);
      if (!isNaN(numCapacity)) {
        where.capacity = numCapacity;
      } else {
        // Gunakan operasi OR jika capacity bukan angka
        where.OR = [
          ...((where.OR as Array<unknown>) || []),
          { description: { contains: capacity, mode: 'insensitive' } },
        ];
      }
    }
    
    if (minPrice > 0) {
      where.price = { gte: minPrice };
    }
    
    if (maxPrice > 0) {
      where.price = { ...((where.price as object) || {}), lte: maxPrice };
    }
    
    if (availability) {
      where.status = availability;
    }

    // Hitung total produk
    const totalProducts = await prisma.product.count({ where });
    
    // Ambil produk dengan pagination
    const products = await prisma.product.findMany({
      where,
      skip: offset,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    // Format response
    return NextResponse.json({
      items: products,
      total: totalProducts,
      page,
      limit,
      totalPages: Math.ceil(totalProducts / limit),
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Gagal mendapatkan daftar produk' },
      { status: 500 }
    );
  }
}

// Endpoint untuk membuat produk baru
export async function POST(request: Request) {
  try {
    console.log("POST /api/products diakses");
    
    const session = await auth();
    console.log("Session user:", session?.user?.email);
    
    // Cek autentikasi
    if (!session?.user) {
      return NextResponse.json(
        { error: "Anda harus login untuk membuat produk" },
        { status: 401 }
      );
    }
    
    // Cek role pengguna
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Anda tidak memiliki izin untuk membuat produk" },
        { status: 403 }
      );
    }
    
    // Cek tipe konten dan parse data
    const contentType = request.headers.get('content-type') || '';
    console.log("Content-Type:", contentType);
    
    let data;
    
    // FormData tidak selalu ditandai dengan 'multipart/form-data' di Next.js
    try {
      // Coba parse sebagai FormData terlebih dahulu
      const formData = await request.formData();
      console.log("Berhasil parse FormData");
      
      // Ekstrak data dari FormData
      data = {
        name: String(formData.get('name') || ''),
        capacity: Number(formData.get('capacity')) || 0,
        price: Number(formData.get('price')) || 0,
        stock: Number(formData.get('stock')) || 0,
        description: formData.get('description') ? String(formData.get('description')) : null,
        imageUrl: formData.get('imageUrl') ? String(formData.get('imageUrl')) : null,
        category: formData.get('category') ? String(formData.get('category')) : null,
        status: "AVAILABLE"
      };
      
      console.log("Data dari FormData:", data);
    } catch (formError) {
      console.log("Gagal parse FormData:", formError);
      // Jika gagal parse FormData, coba parse sebagai JSON
      const text = await request.text();
      try {
        data = JSON.parse(text);
        console.log("Berhasil parse JSON:", data);
      } catch (jsonError) {
        console.error("Gagal parse request body:", jsonError);
        return NextResponse.json(
          { error: "Format data tidak valid" },
          { status: 400 }
        );
      }
    }
    
    // Validasi data dengan schema
    const validatedData = ProductSchema.safeParse(data);
    
    if (!validatedData.success) {
      console.log("Validasi data gagal:", validatedData.error.format());
      return NextResponse.json(
        { error: "Data produk tidak valid", details: validatedData.error.format() },
        { status: 400 }
      );
    }
    
    // Buat produk baru
    const product = await prisma.product.create({
      data: {
        ...validatedData.data,
        userId: session.user.id,
      },
    });
    
    console.log("Produk berhasil dibuat:", product.id);
    
    // Revalidasi path untuk mengupdate cache
    revalidatePath('/user/catalog');
    revalidatePath('/admin/products');
    
    return NextResponse.json(product, { status: 201 });
  } catch (error) {
    console.error("Error creating product:", error);
    return NextResponse.json(
      { error: "Gagal membuat produk", message: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
