import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { AdminHeader } from "@/app/components/admin/admin-header";
import { LogoutButton } from "../_components/logout-button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";

export default async function AdminProfilePage() {
  const session = await auth();
  
  if (!session?.user || session.user.role !== 'ADMIN') {
    redirect('/login');
  }

  return (
    <>
      <AdminHeader title="Profile" description="Kelola informasi akun Anda" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Nama</label>
              <p className="text-lg">{session?.user?.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <p className="text-lg">{session?.user?.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Role</label>
              <p className="text-lg capitalize">{session?.user?.role}</p>
            </div>
          </div>
        </div>

        {/* Kartu Logout */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-red-600">Keluar Aplikasi</CardTitle>
            <CardDescription>
              Keluar dari aplikasi Rental Genset
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Klik tombol di bawah untuk keluar dari akun admin Anda. Anda akan dialihkan ke halaman login.
            </p>
            <LogoutButton />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
