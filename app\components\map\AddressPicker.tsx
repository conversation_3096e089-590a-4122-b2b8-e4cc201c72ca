"use client";

import { useState, useCallback } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Card, CardContent } from "@/app/components/ui/card";
import { Textarea } from "@/app/components/ui/textarea";
import { LuMapPin, LuCrosshair, LuCheck, LuInfo, LuPencil, LuX, LuRotateCw } from "react-icons/lu";
import { AddressPickerProps, LocationData, SearchResult } from "@/lib/types/map";
import { MapDisplay } from "@/app/components/map/MapDisplay";
import { MapSearch } from "@/app/components/map/MapSearch";
import useGeoLocation from "@/lib/hooks/useGeoLocation";
import { useReverseGeocode } from "@/lib/hooks/useReverseGeocode";

// Koordinat default untuk Nusa Tenggara Barat (Pusat Kota Mataram)
const DEFAULT_LATITUDE = -8.5833;
const DEFAULT_LONGITUDE = 116.1167;

export function AddressPicker({ onSelectLocation, defaultAddress = "" }: AddressPickerProps) {
  // State untuk koordinat yang dipilih
  const [coordinates, setCoordinates] = useState({
    lat: DEFAULT_LATITUDE,
    lng: DEFAULT_LONGITUDE,
  });

  // State untuk alamat yang dipilih
  const [address, setAddress] = useState(defaultAddress);
  const [editableAddress, setEditableAddress] = useState(defaultAddress);
  const [isEditingAddress, setIsEditingAddress] = useState(false);

  // State untuk loading dan error handling
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  // Handler untuk pencarian (kita hanya perlu fungsi ini sebagai prop, tidak perlu implementasi)
  const handleSearchResults = useCallback(() => {
    // Tidak perlu implementasi
  }, []);

  // Gunakan hook untuk mendapatkan lokasi pengguna dan reverse geocoding
  const { getCurrentPosition, positionError, clearPositionError } = useGeoLocation();
  const { getAddressFromCoordinates, isLoading: isGeocodingLoading, error: geocodingError } = useReverseGeocode();

  // Handler saat marker di drag
  const handleMarkerDrag = useCallback((location: LocationData) => {
    console.log("Marker digeser ke:", location);
    setCoordinates({ lat: location.lat, lng: location.lng });
    setAddress(location.address);
    setEditableAddress(location.address);

    // Kirim lokasi terbaru ke parent komponen
    onSelectLocation(location);
  }, [onSelectLocation]);

  // Handler untuk menggunakan lokasi saat ini dengan reverse geocoding
  const handleUseCurrentLocation = useCallback(async () => {
    try {
      setIsGettingLocation(true);
      setLocationError(null);
      clearPositionError();

      console.log("Memulai proses mendapatkan lokasi pengguna...");

      // Dapatkan koordinat GPS pengguna
      const position = await getCurrentPosition();
      if (position) {
        const { latitude, longitude } = position.coords;
        console.log("Koordinat pengguna ditemukan:", latitude, longitude);

        // Update koordinat terlebih dahulu
        setCoordinates({
          lat: latitude,
          lng: longitude
        });

        // Lakukan reverse geocoding untuk mendapatkan alamat yang dapat dibaca
        console.log("Memulai reverse geocoding...");
        const humanReadableAddress = await getAddressFromCoordinates(latitude, longitude);

        if (humanReadableAddress) {
          console.log("Alamat berhasil ditemukan:", humanReadableAddress);

          // Update alamat dengan hasil reverse geocoding
          setAddress(humanReadableAddress);
          setEditableAddress(humanReadableAddress);

          // Kirim lokasi lengkap ke parent komponen
          onSelectLocation({
            lat: latitude,
            lng: longitude,
            address: humanReadableAddress
          });
        } else {
          // Jika reverse geocoding gagal, gunakan alamat fallback
          const fallbackAddress = `Lokasi di koordinat ${latitude.toFixed(6)}, ${longitude.toFixed(6)}, Nusa Tenggara Barat, Indonesia`;
          setAddress(fallbackAddress);
          setEditableAddress(fallbackAddress);

          onSelectLocation({
            lat: latitude,
            lng: longitude,
            address: fallbackAddress
          });
        }
      }
    } catch (error) {
      console.error("Error mendapatkan lokasi pengguna:", error);
      const errorMessage = error instanceof Error ? error.message : "Gagal mendapatkan lokasi Anda";
      setLocationError(errorMessage);
    } finally {
      setIsGettingLocation(false);
    }
  }, [getCurrentPosition, clearPositionError, getAddressFromCoordinates, onSelectLocation]);

  // Handler saat memilih hasil pencarian
  const handleSelectSearchResult = useCallback((result: SearchResult) => {
    console.log("Hasil pencarian dipilih:", result);

    // Update koordinat dan alamat
    setCoordinates({
      lat: result.lat,
      lng: result.lon
    });

    setAddress(result.display_name);
    setEditableAddress(result.display_name);

    // Kirim lokasi terbaru ke parent komponen
    onSelectLocation({
      lat: result.lat,
      lng: result.lon,
      address: result.display_name
    });
  }, [onSelectLocation]);

  // Handler untuk mode edit alamat
  const enableAddressEdit = useCallback(() => {
    setIsEditingAddress(true);
  }, []);

  const saveAddressEdit = useCallback(() => {
    setAddress(editableAddress);
    setIsEditingAddress(false);

    // Kirim lokasi terbaru dengan alamat yang diedit
    onSelectLocation({
      lat: coordinates.lat,
      lng: coordinates.lng,
      address: editableAddress
    });
  }, [editableAddress, coordinates, onSelectLocation]);

  const cancelAddressEdit = useCallback(() => {
    setEditableAddress(address);
    setIsEditingAddress(false);
  }, [address]);

  return (
    <div className="flex flex-col h-full space-y-2 sm:space-y-3">
      {/* Pencarian lokasi */}
      <div className="flex-shrink-0 mb-2 sm:mb-3 rounded-md shadow-sm">
        <MapSearch
          onSearchResult={handleSearchResults}
          onSelectResult={handleSelectSearchResult}
          placeholder="Cari lokasi di NTB..."
          className="w-full text-sm sm:text-base"
        />
      </div>

      {/* Peta - Flexible height for mobile modal */}
      <Card className="flex-1 overflow-hidden flex flex-col">
        <CardContent className="p-2 sm:p-3 flex-1 flex flex-col">
          {/* Container peta dengan responsivitas mobile */}
          <div className="relative w-full mb-2 sm:mb-3 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 flex-1">
            <div className="h-full min-h-[200px] sm:min-h-[250px] w-full">
              <MapDisplay
                latitude={coordinates.lat}
                longitude={coordinates.lng}
                zoom={15}
                markerDraggable={true}
                onMarkerDrag={handleMarkerDrag}
                className="h-full w-full"
              />
            </div>

            {/* Overlay untuk loading state yang lebih baik di mobile */}
            {(isGettingLocation || isGeocodingLoading) && (
              <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center z-10">
                <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 shadow-lg flex items-center gap-2 sm:gap-3">
                  <LuRotateCw className="h-4 w-4 sm:h-5 sm:w-5 animate-spin text-blue-600" />
                  <span className="text-sm sm:text-base font-medium text-gray-700 dark:text-gray-300">
                    {isGettingLocation ? "Mendapatkan Lokasi..." : "Mencari Alamat..."}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Kontrol peta - kompak untuk mobile modal */}
          <div className="flex-shrink-0 flex flex-col gap-2 sm:gap-3 mt-2 mb-2">
            {/* Tombol lokasi saat ini - lebih kompak untuk mobile modal */}
            <Button
              type="button"
              variant="outline"
              onClick={handleUseCurrentLocation}
              disabled={isGettingLocation || isGeocodingLoading}
              className="flex items-center justify-center gap-2 h-9 text-sm font-medium w-full"
            >
              {isGettingLocation || isGeocodingLoading ? (
                <LuRotateCw className="h-4 w-4 animate-spin flex-shrink-0" />
              ) : (
                <LuCrosshair className="h-4 w-4 flex-shrink-0" />
              )}
              <span className="truncate">
                {isGettingLocation
                  ? "Mendapatkan Lokasi..."
                  : isGeocodingLoading
                    ? "Mencari Alamat..."
                    : "Gunakan Lokasi Saat Ini"
                }
              </span>
            </Button>

            {/* Lokasi Info - Responsif: Alamat di mobile, koordinat di desktop */}
            <div className="flex items-center justify-center text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-md px-2 py-1">
              <LuMapPin className="h-3 w-3 mr-1 text-indigo-500 dark:text-indigo-400 flex-shrink-0" />

              {/* Tampilkan alamat di mobile (sm dan di bawah) */}
              <span className="block sm:hidden text-xs leading-tight max-w-full mobile-address-display">
                {address ? (
                  <span
                    className="break-words text-gray-700 dark:text-gray-300 mobile-address-text"
                    title={address}
                  >
                    {(() => {
                      // Smart truncation berdasarkan panjang alamat
                      if (address.length <= 45) return address;
                      if (address.length <= 60) return `${address.substring(0, 42)}...`;

                      // Untuk alamat panjang, coba potong di koma atau titik terdekat
                      const truncated = address.substring(0, 40);
                      const lastComma = truncated.lastIndexOf(',');
                      const lastPeriod = truncated.lastIndexOf('.');
                      const cutPoint = Math.max(lastComma, lastPeriod);

                      if (cutPoint > 20) {
                        return `${address.substring(0, cutPoint)}...`;
                      }
                      return `${truncated}...`;
                    })()}
                  </span>
                ) : isGeocodingLoading ? (
                  <span className="italic text-blue-600 dark:text-blue-400 mobile-address-loading">
                    Alamat sedang dimuat...
                  </span>
                ) : (
                  <span className="italic text-gray-400 dark:text-gray-500">
                    Pilih lokasi di peta
                  </span>
                )}
              </span>

              {/* Tampilkan koordinat di desktop (sm ke atas) */}
              <span className="hidden sm:block font-mono text-xs">
                {coordinates.lat.toFixed(4)}, {coordinates.lng.toFixed(4)}
              </span>
            </div>
          </div>

          {/* Error handling untuk lokasi - responsif */}
          {(locationError || positionError || geocodingError) && (
            <div className="mb-3 p-3 sm:p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <div className="flex items-start gap-2 sm:gap-3">
                <LuInfo className="h-4 w-4 sm:h-5 sm:w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                <p className="text-sm sm:text-base text-red-700 dark:text-red-300 leading-relaxed">
                  {locationError || positionError || geocodingError}
                </p>
              </div>
            </div>
          )}

          {/* Alamat - kompak untuk mobile modal */}
          <div className="flex-shrink-0 mt-2">
            <div className="flex justify-between items-center gap-2 mb-2">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Alamat Terpilih
              </h3>

              {!isEditingAddress ? (
                <Button
                  type="button"
                  variant="ghost"
                  onClick={enableAddressEdit}
                  size="sm"
                  className="h-8 px-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                >
                  <LuPencil className="h-3 w-3 mr-1" />
                  <span className="text-xs">Edit</span>
                </Button>
              ) : (
                <div className="flex gap-1">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={cancelAddressEdit}
                    size="sm"
                    className="h-8 px-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                  >
                    <LuX className="h-3 w-3 mr-1" />
                    <span className="text-xs">Batal</span>
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={saveAddressEdit}
                    size="sm"
                    className="h-8 px-2 text-green-600 dark:text-green-400"
                  >
                    <LuCheck className="h-3 w-3 mr-1" />
                    <span className="text-xs">Simpan</span>
                  </Button>
                </div>
              )}
            </div>

            {isEditingAddress ? (
              <Textarea
                value={editableAddress}
                onChange={(e) => setEditableAddress(e.target.value)}
                placeholder="Masukkan alamat lengkap..."
                className="w-full text-sm dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700 min-h-[60px]"
                rows={2}
              />
            ) : (
              <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-md text-sm text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-700 min-h-[40px] flex items-center">
                {address || (
                  <span className="text-gray-500 dark:text-gray-400 italic text-xs">
                    Alamat belum dipilih
                  </span>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default AddressPicker;
