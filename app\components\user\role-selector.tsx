"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Shield, User } from "lucide-react";
import { cn } from "@/lib/utils/cn";

interface RoleSelectorProps {
    userId: string;
    currentRole: string;
    onUpdate: () => void;
}

// Map database enum values to display values
const roleMapping = {
    'ADMIN': 'admin',
    'USER': 'user',
    'admin': 'ADMIN',
    'user': 'USER'
} as const;

const roleDisplayConfig = {
    admin: {
        label: 'Admin',
        icon: Shield,
        badgeVariant: 'destructive' as const,
        badgeClass: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
        selectIconColor: 'text-red-600 dark:text-red-400'
    },
    user: {
        label: 'User',
        icon: User,
        badgeVariant: 'secondary' as const,
        badgeClass: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800',
        selectIconColor: 'text-blue-600 dark:text-blue-400'
    }
};

export function RoleSelector({ userId, currentRole, onUpdate }: RoleSelectorProps) {
    const { data: session } = useSession();
    // Convert database enum to display value
    const displayRole = roleMapping[currentRole as keyof typeof roleMapping] || currentRole.toLowerCase();
    const [role, setRole] = useState(displayRole);
    const [isLoading, setIsLoading] = useState(false);

    // Logika untuk menentukan apakah role dapat diubah
    const currentUserId = session?.user?.id;
    const isCurrentUser = userId === currentUserId;
    const isTargetAdmin = currentRole === 'ADMIN' || currentRole === 'admin';
    const canChangeRole = !isCurrentUser && !isTargetAdmin;

    const handleChange = async (newRole: string) => {
        if (!canChangeRole) {
            if (isCurrentUser) {
                toast.error('Tidak dapat mengubah role diri sendiri');
            } else if (isTargetAdmin) {
                toast.error('Tidak dapat mengubah role admin lain');
            }
            return;
        }

        setIsLoading(true);

        try {
            // Convert display value back to database enum
            const dbRole = roleMapping[newRole as keyof typeof roleMapping] || newRole.toUpperCase();

            const response = await fetch(`/api/users/${userId}/role`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ role: dbRole }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Gagal mengupdate role');
            }

            setRole(newRole);
            onUpdate();
            toast.success('Role berhasil diupdate');
        } catch (error) {
            console.error('Error updating role:', error);
            toast.error(error instanceof Error ? error.message : 'Gagal mengupdate role');
            setRole(displayRole); // Reset ke role sebelumnya jika gagal
        } finally {
            setIsLoading(false);
        }
    };

    const currentConfig = roleDisplayConfig[role as keyof typeof roleDisplayConfig];
    const IconComponent = currentConfig?.icon || User;

    return (
        <div className="flex items-center gap-2">
            <Badge
                className={cn(
                    "flex items-center gap-1 px-2 py-1 text-xs font-medium border",
                    currentConfig?.badgeClass || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700'
                )}
            >
                <IconComponent className="h-3 w-3" />
                {currentConfig?.label || role}
                {isCurrentUser && <span className="text-xs opacity-75">(Anda)</span>}
            </Badge>

            <Select
                value={role}
                onValueChange={handleChange}
                disabled={isLoading || !canChangeRole}
            >
                <SelectTrigger
                    className={cn(
                        "w-[100px] h-8 text-xs border-border bg-background hover:bg-accent hover:text-accent-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50",
                        !canChangeRole && "cursor-not-allowed"
                    )}
                    title={!canChangeRole ? (isCurrentUser ? "Tidak dapat mengubah role diri sendiri" : "Tidak dapat mengubah role admin lain") : undefined}
                >
                    <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-popover border-border shadow-lg">
                    <SelectItem
                        value="admin"
                        className="text-xs hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                    >
                        <div className="flex items-center gap-2">
                            <Shield className="h-3 w-3 text-red-600 dark:text-red-400" />
                            <span className="text-foreground">Admin</span>
                        </div>
                    </SelectItem>
                    <SelectItem
                        value="user"
                        className="text-xs hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                    >
                        <div className="flex items-center gap-2">
                            <User className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                            <span className="text-foreground">User</span>
                        </div>
                    </SelectItem>
                </SelectContent>
            </Select>
        </div>
    );
}
