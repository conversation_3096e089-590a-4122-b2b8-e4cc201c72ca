import { Metada<PERSON> } from "next";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { LuArrowLeft, LuPower, LuInfo, LuZap, LuCalendar, LuTruck } from "react-icons/lu";

export const metadata: Metadata = {
  title: "Detail Produk",
  description: "Informasi detail produk genset",
};

async function getProductById(id: string) {
  try {
    // Gunakan URL relatif
    const url = `/api/products/${id}`;

    const response = await fetch(url, {
      cache: 'no-store',
      next: { revalidate: 0 } // Pastikan data selalu terbaru
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Product fetch error:', response.status, errorData);
      throw new Error(`Failed to fetch product: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error loading product:', error);
    return null;
  }
}

export default async function ProductDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await auth();
  if (!session?.user) {
    redirect('/login');
  }

  const { id } = await params;
  const product = await getProductById(id);

  if (!product) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold mb-4">Produk Tidak Ditemukan</h1>
        <p className="text-muted-foreground mb-6">
          Produk yang Anda cari tidak tersedia atau telah dihapus.
        </p>
        <Link href="/user/catalog">
          <Button variant="default">Kembali ke Katalog</Button>
        </Link>
      </div>
    );
  }

  const productStatus = product.status === "AVAILABLE" ? "Tersedia" : "Tidak Tersedia";
  const capacityValue = typeof product.capacity === 'number'
    ? `${product.capacity} kVA`
    : product.capacity;

  // Estimasi konsumsi BBM
  const numCapacity = typeof product.capacity === 'number'
    ? product.capacity
    : parseFloat(String(product.capacity));

  const fuelConsumption = !isNaN(numCapacity)
    ? `${(numCapacity * 0.2).toFixed(1)} L/jam`
    : '0 L/jam';

  return (
    <>
      <div className="flex items-center mb-8">
        <Link href="/user/catalog" className="mr-4">
          <Button variant="outline" size="icon" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">
            <LuArrowLeft className="h-4 w-4 dark:text-gray-300" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Detail Produk</h1>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <Card className="overflow-hidden lg:col-span-2">
          <div className="aspect-video w-full overflow-hidden bg-gray-100 relative">
            {product.imageUrl || product.image ? (
              <Image
                src={product.imageUrl || product.image}
                alt={product.name}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
                className="object-cover"
                onError={(e) => {
                  // Jika gambar tidak bisa dimuat, ganti dengan placeholder
                  (e.target as HTMLImageElement).src = "https://via.placeholder.com/800x400?text=Genset";
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-200">
                <span className="text-gray-400">Tidak ada gambar</span>
              </div>
            )}
          </div>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">{product.name}</CardTitle>
              <Badge variant={product.status === "AVAILABLE" ? "default" : "secondary"}>
                {productStatus}
              </Badge>
            </div>
            <CardDescription>
              {new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                maximumFractionDigits: 0
              }).format(product.price)} / hari
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Deskripsi</h3>
              <p className="text-muted-foreground">
                {product.description || "Tidak ada deskripsi tersedia untuk produk ini."}
              </p>

              <h3 className="text-lg font-semibold mt-6">Spesifikasi</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <LuPower className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Kapasitas</p>
                    <p className="text-sm text-muted-foreground">{capacityValue}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <LuInfo className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Tipe</p>
                    <p className="text-sm text-muted-foreground">{product.category || "Standard"}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <LuZap className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Konsumsi BBM</p>
                    <p className="text-sm text-muted-foreground">{fuelConsumption}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <LuTruck className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Pengiriman</p>
                    <p className="text-sm text-muted-foreground">Tersedia</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Sewa Sekarang</CardTitle>
            <CardDescription>
              Pilih tanggal dan lakukan pemesanan
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <LuCalendar className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Ketersediaan</p>
                <p className="text-sm text-muted-foreground">
                  {product.status === "AVAILABLE"
                    ? "Tersedia untuk disewa"
                    : "Tidak tersedia saat ini"}
                </p>
              </div>
            </div>

            <div className="pt-4">
              <p className="font-medium mb-2">Harga Sewa</p>
              <div className="text-2xl font-bold text-green-600">
                {new Intl.NumberFormat('id-ID', {
                  style: 'currency',
                  currency: 'IDR',
                  maximumFractionDigits: 0
                }).format(product.price)}
                <span className="text-sm font-normal text-muted-foreground"> / hari</span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Link href={`/user/rentals/new/${params.id}`} className="w-full">
              <Button
                className="w-full bg-green-600 hover:bg-green-700"
                disabled={product.status !== "AVAILABLE"}
                size="lg"
              >
                {product.status === "AVAILABLE" ? "Sewa Sekarang" : "Tidak Tersedia"}
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </>
  );
}