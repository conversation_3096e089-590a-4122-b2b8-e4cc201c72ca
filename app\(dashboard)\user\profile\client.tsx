"use client";

import { useFormStatus } from "react-dom";
import { useEffect, useState, useCallback } from "react";
import { Button } from "@/app/components/ui/button";
import { toast } from "sonner";

export function SubmitButton({
  children,
  className,
  pendingText = "Menyimpan...",
}: {
  children: React.ReactNode;
  className?: string;
  pendingText?: string;
}) {
  const { pending } = useFormStatus();
  
  return (
    <Button type="submit" className={className} disabled={pending}>
      {pending ? pendingText : children}
    </Button>
  );
}

// Fungsi untuk menyimpan data profil ke localStorage
export function saveProfileToLocalStorage(data: { address: string, bio: string, image?: string }) {
  try {
    console.log('Saving to localStorage:', data);
    localStorage.setItem('profileData', JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Failed to save profile data to localStorage:', error);
    return false;
  }
}

export function FormStatus() {
  const { pending } = useFormStatus();
  const [wasPending, setWasPending] = useState(false);
  const [success, setSuccess] = useState(false);
  
  // Fungsi untuk menampilkan toast diluar dari render cycle
  const showSuccessToast = useCallback(() => {
    toast.success("Perubahan berhasil disimpan");
  }, []);
  
  // Fungsi untuk merefresh halaman
  const refreshPage = useCallback(() => {
    setTimeout(() => {
      // Sebelum refresh, coba deteksi perubahan pada URL gambar
      try {
        // Cari elemen img dalam dokumen yang berisi URL gambar baru
        const imgElements = document.querySelectorAll('img');
        for (const img of imgElements) {
          const src = img.getAttribute('src');
          if (src && (src.includes('vercel') || src.includes('blob'))) {
            console.log('Found possible profile image with URL:', src);
            // Simpan URL gambar ke localStorage
            const storedData = localStorage.getItem('profileData');
            const profileData = storedData ? JSON.parse(storedData) : {};
            
            saveProfileToLocalStorage({
              ...profileData,
              image: src
            });
            break;
          }
        }
      } catch (error) {
        console.error('Error detecting image URL:', error);
      }
      
      // Lakukan hard reload halaman untuk memastikan perubahan diaplikasikan
      window.location.href = window.location.pathname;
    }, 500);
  }, []);
  
  useEffect(() => {
    if (wasPending && !pending) {
      // Form telah selesai dikirim, tandai sebagai sukses
      setSuccess(true);
      
      // Coba untuk mendapatkan data dari form terakhir
      try {
        const formElem = document.querySelector('form') as HTMLFormElement;
        if (formElem) {
          const formData = new FormData(formElem);
          const address = formData.get('address') as string;
          const bio = formData.get('bio') as string;
          
          if (address) {
            // Menyimpan data profil ke localStorage
            const storedData = localStorage.getItem('profileData');
            const profileData = storedData ? JSON.parse(storedData) : {};
            
            saveProfileToLocalStorage({
              ...profileData,
              address,
              bio: bio || '',
            });
            console.log('Profile data saved to localStorage:', { address, bio });
          }
        }
      } catch (error) {
        console.error('Error saving form data to localStorage:', error);
      }
    }
    
    setWasPending(pending);
  }, [pending, wasPending]);
  
  // Effect terpisah untuk menangani toast dan refresh
  useEffect(() => {
    if (success) {
      // Jadwalkan toast setelah render
      const timeoutId = setTimeout(() => {
        showSuccessToast();
        
        // Jadwalkan refresh setelah toast
        refreshPage();
      }, 100);
      
      // Reset state
      setSuccess(false);
      
      return () => clearTimeout(timeoutId);
    }
  }, [success, showSuccessToast, refreshPage]);
  
  return null;
} 
